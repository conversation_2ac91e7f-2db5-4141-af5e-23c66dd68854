import {
	Injectable,
	type NestMiddleware,
	UnauthorizedException,
} from '@nestjs/common';
import type { NextFunction, Request, Response } from 'express';

import { RedisService } from '../shared/services/redis.service';
import { RedisKeyManagerService } from '../shared/services/redis-key-manager.service';

@Injectable()
export class JwtBlacklistMiddleware implements NestMiddleware {
	constructor(
		private readonly redisService: RedisService,
		private readonly redisKeyManager: RedisKeyManagerService,
	) {}

	async use(req: Request, _res: Response, next: NextFunction) {
		const authHeader = req.headers.authorization;

		if (authHeader && authHeader.startsWith('Bearer ')) {
			const token = authHeader.substring(7);

			try {
				// Check if token is blacklisted
				const blacklistKey = this.redisKeyManager.auth.blacklistToken(token);
				const isBlacklisted = await this.redisService.get<string>(blacklist<PERSON>ey);

				if (isBlacklisted === 'true') {
					throw new UnauthorizedException('Token has been revoked');
				}
			} catch (error) {
				if (error instanceof UnauthorizedException) {
					throw error;
				}
				// If Redis is down, continue (fail open for availability)
				console.warn('[JWT-BLACKLIST] Redis blacklist check failed:', error);
			}
		}

		next();
	}
}
