import bcrypt from 'bcrypt';
import type { Request } from 'express';

/**
 * generate hash from password or string
 * @param {string} password
 * @returns {string}
 */
export function generateHash(password: string): string {
	return bcrypt.hashSync(password, 10);
}

/**
 * validate text with hash
 * @param {string} password
 * @param {string} hash
 * @returns {Promise<boolean>}
 */
export function validateHash(
	password: string | undefined,
	hash: string | undefined | null,
): Promise<boolean> {
	if (!password || !hash) {
		return Promise.resolve(false);
	}

	return bcrypt.compare(password, hash);
}

export function getVariableName<TResult>(
	getVar: () => TResult,
): string | undefined {
	const m = /\(\)=>(.*)/.exec(
		getVar.toString().replaceAll(/(\r\n|\n|\r|\s)/gm, ''),
	);

	if (!m) {
		throw new Error(
			'The function does not contain a statement matching \'return variableName;\'',
		);
	}

	const fullMemberName = m[1]!;

	const memberParts = fullMemberName.split('.');

	return memberParts.at(-1);
}

export function getIp(request: Request): string {
	// Cloudflare header
	const cfIp = request.headers['cf-connecting-ip'];
	if (typeof cfIp === 'string') {
		return cfIp;
	}

	let ip = '127.0.0.1';

	// Standard proxy header
	const forwarded = request.headers['x-forwarded-for'];
	if (forwarded !== undefined) {
		if (typeof forwarded === 'string') {
			ip = (forwarded.split(',')[0] || ip).trim();
		} else if (Array.isArray(forwarded) && forwarded.length > 0) {
			ip = forwarded[0] || ip;
		}
	} else {
		// Express/Socket IP
		ip = request.ip || request.socket?.remoteAddress || ip;
	}
	// Normalize IPv6 loopback
	if (ip === '::1' || ip === '0:0:0:0:0:0:0:1') {
		ip = '127.0.0.1';
	}

	// If IPv6 mapped IPv4 (::ffff:127.0.0.1) → extract IPv4
	if (ip.startsWith('::ffff:')) {
		ip = ip.substring(7);
	}

	return ip;
}

export function isEmail(value: string): boolean {
	return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
}

export function isVietnamesePhone(value: string): boolean {
	return /^84[0-9]{9}$|^84[0-9]{10}$/.test(value);
}

export function isEmailOrVietnamesePhone(value: string): boolean {
	return isEmail(value) || isVietnamesePhone(value);
}

/**
 * Determine contact type from value
 */
export function getContactType(value: string): 'email' | 'phone' | 'invalid' {
	if (isEmail(value)) return 'email';
	if (isVietnamesePhone(value)) return 'phone';
	return 'invalid';
}
