import { EmailOrPhoneField } from '@decorators/field.decorators';
import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class SendOtpDto {
	@EmailOrPhoneField({
		example: '<EMAIL> or 84905060708',
		description: 'Email address or Vietnamese phone number to send OTP to',
	})
	target!: string;
}

export class VerifyOtpDto {
	@EmailOrPhoneField({
		example: '<EMAIL> or 84905060708',
		description:
			'Email address or Vietnamese phone number that received the OTP',
	})
	target!: string;

	@ApiProperty({
		example: '123456',
		description: 'The 6-digit OTP code',
	})
	@IsString()
	otp!: string;
}
