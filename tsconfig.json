{
  "compilerOptions": {
    "module": "ESNext",
    "lib": ["ESNext"],
    "moduleResolution": "Node",
    "declaration": true,
    "noImplicitAny": true,
    "removeComments": true,
    "skipLibCheck": true,
    "allowImportingTsExtensions": true,
    "noEmit": true,
    "importHelpers": true,
    "allowSyntheticDefaultImports": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "verbatimModuleSyntax": true,
    "useDefineForClassFields": true,
    "allowUnreachableCode": false,
    "forceConsistentCasingInFileNames": true,
    "noImplicitReturns": true,
    "isolatedModules": true,
//    "exactOptionalPropertyTypes": true,
    "noImplicitThis": true,
//    "noPropertyAccessFromIndexSignature": true,
    "noUnusedParameters": true,
    "noUnusedLocals": true,
    "strictPropertyInitialization": true,
    "target": "ESNext",
    "noFallthroughCasesInSwitch": true,
    "sourceMap": true,
    "noEmitHelpers": true,
    "noUncheckedIndexedAccess": true,
    "outDir": "./dist",
    "baseUrl": "./src",
    "paths": {
      "@common/*": ["common/*"],
      "@constants/*": ["constants/*"],
      "@decorators/*": ["decorators/*"],
      "@modules/*": ["modules/*"],
      "@shared/*": ["shared/*"],
    },
    "incremental": true,
    "strict": true,
    "strictNullChecks": true
  },
  "ts-node": {
    "experimentalSpecifierResolution": "node",
    "transpileOnly": true,
    "esm": true,
},
  "include": [
    "src/**/*",
  ],
  "exclude": [
    "node_modules",
    "**/*.spec.ts"
  ]
}
