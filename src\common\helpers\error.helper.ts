import { isEmail, isVietnamesePhone } from '@common/utils';
import { UserAccountStatus } from '@constants/user';
import {
	BadRequestException,
	ConflictException,
	ForbiddenException,
	NotFoundException,
} from '@nestjs/common';

/**
 * Shared error handling utilities
 */
export class ErrorHelper {
	/**
	 * Validate contact format and throw appropriate error
	 */
	static validateContactFormat(contact: string): 'email' | 'phone' {
		if (!contact) {
			throw new BadRequestException('Contact is required.');
		}

		const isEmailContact = isEmail(contact);
		const isPhoneContact = isVietnamesePhone(contact);

		if (!isEmailContact && !isPhoneContact) {
			throw new BadRequestException(
				'Must be a valid email address or Vietnamese phone number (84xxxxxxxxx)',
			);
		}

		return isEmailContact ? 'email' : 'phone';
	}

	/**
	 * Check user account status and throw appropriate error
	 */
	static validateUserAccountStatus(
		user: { status: UserAccountStatus } | null,
		context: 'login' | 'registration' | 'password_reset' = 'login',
	): void {
		if (!user) {
			if (context === 'login' || context === 'password_reset') {
				throw new NotFoundException('User not found');
			}
			return; // For registration, null user is expected
		}

		if (user.status !== UserAccountStatus.ACTIVE) {
			switch (context) {
				case 'login':
					throw new ForbiddenException(
						'Account is not active. Please contact support.',
					);
				case 'registration':
					throw new ConflictException(
						'User with this email/phone already exists',
					);
				case 'password_reset':
					throw new ForbiddenException(
						'Account is not active. Please contact support.',
					);
			}
		}

		// For registration context, active user means conflict
		if (context === 'registration') {
			throw new ConflictException('User with this email/phone already exists');
		}
	}

	/**
	 * Create standardized rate limit error
	 */
	static createRateLimitError(
		type: 'user' | 'ip',
		identifier: string,
		limitTtl: number,
	): BadRequestException {
		const message =
			type === 'user'
				? `Too many requests for ${identifier}. Please try again in ${limitTtl} minutes.`
				: `Too many requests from this IP. Please try again in ${limitTtl} minutes.`;

		return new BadRequestException(message);
	}

	/**
	 * Create standardized OTP error
	 */
	static createOtpError(
		type: 'expired' | 'invalid' | 'max_attempts',
	): BadRequestException {
		switch (type) {
			case 'expired':
				return new BadRequestException(
					'OTP has expired. Please request a new one.',
				);
			case 'invalid':
				return new BadRequestException('Invalid OTP code');
			case 'max_attempts':
				return new BadRequestException(
					'Maximum OTP verification attempts exceeded. Please request a new OTP.',
				);
			default:
				return new BadRequestException('OTP verification failed');
		}
	}

	/**
	 * Create standardized service unavailable error
	 */
	static createServiceUnavailableError(service: string): BadRequestException {
		return new BadRequestException(
			`${service} service is temporarily unavailable. Please try again later.`,
		);
	}

	/**
	 * Safe error logging (removes sensitive data)
	 */
	static sanitizeErrorForLogging(error: any): string {
		if (error instanceof Error) {
			return error.message;
		}

		const errorStr = String(error);
		// Remove potential sensitive data patterns
		return errorStr
			.replace(/password[=:]\s*[^\s,}]+/gi, 'password=***')
			.replace(/token[=:]\s*[^\s,}]+/gi, 'token=***')
			.replace(/key[=:]\s*[^\s,}]+/gi, 'key=***');
	}
}
