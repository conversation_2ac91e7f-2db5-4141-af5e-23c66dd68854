import { HelpersModule } from '@common/helpers/helpers.module';
import { OtpModule } from '@modules/otp/otp.module';
import { SysConfigModule } from '@modules/sys-config/sys-config.module';
import { UserModule } from '@modules/user/user.module';
import { HttpModule } from '@nestjs/axios';
import { forwardRef, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ApiConfigService } from '@shared/services/api-config.service';
import { SecurityLoggerService } from '@shared/services/security-logger.service';

import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { FacebookStrategy } from './facebook.strategy';
import { GoogleStrategy } from './google.strategy';
import { JwtStrategy } from './jwt.strategy';
import { PublicStrategy } from './public.strategy';

@Module({
	imports: [
		forwardRef(() => UserModule),
		PassportModule.register({ defaultStrategy: 'jwt' }),
		JwtModule.registerAsync({
			useFactory: (configService: ApiConfigService) => ({
				privateKey: configService.authConfig.privateKey,
				publicKey: configService.authConfig.publicKey,
				signOptions: {
					algorithm: 'RS256',
					expiresIn: configService.getNumber('JWT_EXPIRATION_TIME'),
				},
				verifyOptions: {
					algorithms: ['RS256'],
				},
				// if you want to use token with expiration date
				// signOptions: {
				//     expiresIn: configService.getNumber('JWT_EXPIRATION_TIME'),
				// },
			}),
			inject: [ApiConfigService],
		}),
		PassportModule,
		HttpModule,
		forwardRef(() => OtpModule),
		SysConfigModule,
		HelpersModule,
	],
	controllers: [AuthController],
	providers: [
		AuthService,
		JwtStrategy,
		PublicStrategy,
		GoogleStrategy,
		FacebookStrategy,
		SecurityLoggerService,
	],
	exports: [JwtModule, AuthService],
})
export class AuthModule {}
