import {
	BadRequestException,
	Injectable,
	Logger,
	NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { RedisService } from '@shared/services/redis.service';
import { RedisKeyManagerService } from '@shared/services/redis-key-manager.service';
import { Repository } from 'typeorm';

import {
	ConfigCacheStatsDto,
	GetConfigValueDto,
	SetConfigValueDto,
} from './dto/sys-config.dto';
import { SysConfigEntity } from './entities/sys-config.entity';

@Injectable()
export class SysConfigService {
	private readonly logger = new Logger(SysConfigService.name);
	private readonly CACHE_TTL = 3600; // 1 hour

	// Cache statistics
	private cacheStats = {
		hits: 0,
		misses: 0,
		totalRequests: 0,
	};

	constructor(
		@InjectRepository(SysConfigEntity)
		private readonly configRepository: Repository<SysConfigEntity>,
		private readonly redisService: RedisService,
		private readonly redisKeyManager: RedisKeyManagerService,
	) {
		this.initializeCacheStats();
	}

	private async initializeCacheStats(): Promise<void> {
		try {
			const statsKey = this.redisKeyManager.sysConfig.stats();
			const stats = await this.redisService.get(statsKey);
			if (stats && typeof stats === 'string') {
				this.cacheStats = JSON.parse(stats);
			}
		} catch (error) {
			this.logger.warn('Failed to load cache stats from Redis', error);
		}
	}

	private async updateCacheStats(isHit: boolean): Promise<void> {
		this.cacheStats.totalRequests++;
		if (isHit) {
			this.cacheStats.hits++;
		} else {
			this.cacheStats.misses++;
		}

		// Update Redis stats every 10 requests to avoid too frequent writes
		if (this.cacheStats.totalRequests % 10 === 0) {
			try {
				const statsKey = this.redisKeyManager.sysConfig.stats();
				await this.redisService.set(
					statsKey,
					JSON.stringify(this.cacheStats),
					86400, // 24 hours
				);
			} catch (error) {
				this.logger.warn('Failed to update cache stats in Redis', error);
			}
		}
	}

	private getCacheKey(key: string): string {
		return this.redisKeyManager.sysConfig.value(key);
	}

	/**
	 * Get configuration value with type conversion
	 */
	async getValue<T = any>(key: string): Promise<T> {
		const cacheKey = this.getCacheKey(key);

		try {
			// Try cache first
			const cachedConfig =
				await this.redisService.get<SysConfigEntity>(cacheKey);
			if (cachedConfig) {
				await this.updateCacheStats(true);
				const entity = Object.assign(new SysConfigEntity(), cachedConfig);
				return entity.getTypedValue<T>();
			}
		} catch (error) {
			this.logger.warn(`Cache read failed for key ${key}`, error);
		}

		await this.updateCacheStats(false);

		// Fallback to database
		const config = await this.configRepository.findOne({ where: { key } });
		if (!config) {
			throw new NotFoundException(`Configuration key "${key}" not found`);
		}

		// Cache the result
		try {
			await this.redisService.set(cacheKey, config, this.CACHE_TTL);
		} catch (error) {
			this.logger.warn(`Cache write failed for key ${key}`, error);
		}

		return config.getTypedValue<T>();
	}

	/**
	 * Get configuration value as string (backward compatibility)
	 */
	async getStringValue(key: string): Promise<string> {
		const value = await this.getValue<string>(key);
		return value ?? '';
	}

	/**
	 * Get configuration with metadata for API responses
	 */
	async getConfigForApi(key: string): Promise<GetConfigValueDto> {
		const cacheKey = this.getCacheKey(key);

		try {
			// Try cache first
			const cachedConfig =
				await this.redisService.get<SysConfigEntity>(cacheKey);
			if (cachedConfig) {
				await this.updateCacheStats(true);
				return this.formatConfigForApi(
					Object.assign(new SysConfigEntity(), cachedConfig),
				);
			}
		} catch (error) {
			this.logger.warn(`Cache read failed for key ${key}`, error);
		}

		await this.updateCacheStats(false);

		// Fallback to database
		const config = await this.configRepository.findOne({ where: { key } });
		if (!config) {
			throw new NotFoundException(`Configuration key "${key}" not found`);
		}

		// Cache the result
		try {
			await this.redisService.set(cacheKey, config, this.CACHE_TTL);
		} catch (error) {
			this.logger.warn(`Cache write failed for key ${key}`, error);
		}

		return this.formatConfigForApi(config);
	}

	private formatConfigForApi(config: SysConfigEntity): GetConfigValueDto {
		return {
			key: config.key,
			value: config.getTypedValue(),
			dataType: config.dataType,
		};
	}

	/**
	 * Set configuration value with type handling and caching
	 */
	async setValue(dto: SetConfigValueDto): Promise<SysConfigEntity> {
		const { key, value, description, dataType = 'string', category } = dto;

		// Validate data type and value compatibility
		this.validateValueForDataType(value, dataType);

		let config = await this.configRepository.findOne({ where: { key } });

		if (!config) {
			config = this.configRepository.create({
				key,
				description,
				dataType,
				category,
			});
		} else {
			// Update existing config
			if (description !== undefined) config.description = description;
			if (dataType !== config.dataType) config.dataType = dataType;
			if (category !== undefined) config.category = category;
		}

		// Set typed value
		config.setTypedValue(value, dataType);

		const savedConfig = await this.configRepository.save(config);

		// Update cache
		const cacheKey = this.getCacheKey(key);
		try {
			await this.redisService.set(cacheKey, savedConfig, this.CACHE_TTL);
		} catch (error) {
			this.logger.warn(`Cache update failed for key ${key}`, error);
		}

		this.logger.log(`Configuration updated: ${key} = ${value}`);
		return savedConfig;
	}

	private validateValueForDataType(value: any, dataType: string): void {
		if (value === null || value === undefined) return;

		switch (dataType) {
			case 'number':
				if (isNaN(Number(value))) {
					throw new BadRequestException(
						`Value "${value}" is not a valid number`,
					);
				}
				break;
			case 'boolean':
				if (
					typeof value !== 'boolean' &&
					!['true', 'false'].includes(String(value).toLowerCase())
				) {
					throw new BadRequestException(
						`Value "${value}" is not a valid boolean`,
					);
				}
				break;
			case 'json':
				if (typeof value === 'string') {
					try {
						JSON.parse(value);
					} catch {
						throw new BadRequestException(`Value "${value}" is not valid JSON`);
					}
				}
				break;
		}
	}

	/**
	 * Delete configuration and clear cache
	 */
	async deleteConfig(key: string): Promise<void> {
		const config = await this.configRepository.findOne({ where: { key } });
		if (!config) {
			throw new NotFoundException(`Configuration key "${key}" not found`);
		}

		await this.configRepository.remove(config);

		// Clear cache
		const cacheKey = this.getCacheKey(key);
		try {
			await this.redisService.del(cacheKey);
		} catch (error) {
			this.logger.warn(`Cache deletion failed for key ${key}`, error);
		}

		this.logger.log(`Configuration deleted: ${key}`);
	}

	/**
	 * Get all configurations with optional filtering
	 */
	async findAll(category?: string): Promise<SysConfigEntity[]> {
		const queryBuilder = this.configRepository.createQueryBuilder('config');

		if (category) {
			queryBuilder.where('config.category = :category', { category });
		}

		queryBuilder
			.orderBy('config.category', 'ASC')
			.addOrderBy('config.key', 'ASC');

		return queryBuilder.getMany();
	}

	/**
	 * Get configurations by category
	 */
	async getByCategory(category: string): Promise<SysConfigEntity[]> {
		return this.configRepository.find({
			where: { category },
			order: { key: 'ASC' },
		});
	}

	/**
	 * Bulk set configurations
	 */
	async bulkSet(configs: SetConfigValueDto[]): Promise<SysConfigEntity[]> {
		const results: SysConfigEntity[] = [];

		for (const configDto of configs) {
			const result = await this.setValue(configDto);
			results.push(result);
		}

		return results;
	}

	/**
	 * Clear all cache
	 */
	async clearCache(): Promise<void> {
		try {
			const pattern = 'sys_config:*';
			const keys = await this.redisService.keys(pattern);

			if (keys.length > 0) {
				// Delete keys one by one to avoid spread argument issue
				for (const key of keys) {
					await this.redisService.del(key);
				}
				this.logger.log(`Cleared ${keys.length} cached configurations`);
			}
		} catch (error) {
			this.logger.error('Failed to clear configuration cache', error);
			throw error;
		}
	}

	/**
	 * Get cache statistics
	 */
	async getCacheStats(): Promise<ConfigCacheStatsDto> {
		const pattern = 'sys_config:*';
		const cachedKeys = await this.redisService.keys(pattern);

		const hitRatio =
			this.cacheStats.totalRequests > 0
				? (this.cacheStats.hits / this.cacheStats.totalRequests) * 100
				: 0;

		return {
			totalCached: cachedKeys.length,
			hitCount: this.cacheStats.hits,
			missCount: this.cacheStats.misses,
			hitRatio: Math.round(hitRatio * 100) / 100,
		};
	}

	/**
	 * Preload all configurations into cache
	 */
	async preloadCache(): Promise<void> {
		const configs = await this.configRepository.find();

		for (const config of configs) {
			const cacheKey = this.getCacheKey(config.key);
			try {
				await this.redisService.set(cacheKey, config, this.CACHE_TTL);
			} catch (error) {
				this.logger.warn(
					`Failed to preload cache for key ${config.key}`,
					error,
				);
			}
		}

		this.logger.log(`Preloaded ${configs.length} configurations into cache`);
	}
}
