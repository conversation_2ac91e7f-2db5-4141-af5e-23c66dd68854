import { ClassField } from '@decorators/field.decorators';
import { UserAccountDto } from '@modules/user/dtos/user-account.dto';

import { TokenPayloadDto } from './token-payload.dto.ts';

export class LoginPayloadDto {
	@ClassField(() => UserAccountDto)
	user: UserAccountDto;

	@ClassField(() => TokenPayloadDto)
	accessToken: TokenPayloadDto;

	constructor(user: UserAccountDto, token: TokenPayloadDto) {
		this.user = user;
		this.accessToken = token;
	}
}
