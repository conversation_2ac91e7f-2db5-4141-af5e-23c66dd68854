import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MinLength } from 'class-validator';

export class UserLoginDto {
	@ApiProperty({
		example: '<EMAIL>',
		description: 'Username, email, or phone number',
	})
	@IsString()
	@IsNotEmpty()
	readonly username!: string;

	@ApiProperty({
		example: 'Password123!',
		description: 'User password',
	})
	@IsString()
	@IsNotEmpty()
	@MinLength(1)
	readonly password!: string;
}
