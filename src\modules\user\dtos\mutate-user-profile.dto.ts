import { Gender } from '@constants/gender';
import {
	DateFieldOptional,
	EnumFieldOptional,
	StringFieldOptional,
} from '@decorators/field.decorators';

export class MutateUserProfileDto {
	@StringFieldOptional({ nullable: true })
	displayName?: string | null;

	@EnumFieldOptional(() => Gender, { nullable: true })
	gender?: Gender | null;

	@StringFieldOptional({ nullable: true })
	avatarUrl?: string | null;

	@DateFieldOptional({ nullable: true })
	dob?: string | null;

	@StringFieldOptional({ nullable: true })
	address?: string | null;

	@StringFieldOptional({ nullable: true })
	identifierNumber?: string | null;
}
