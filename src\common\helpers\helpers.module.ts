import { SysConfigModule } from '@modules/sys-config/sys-config.module';
import { Module } from '@nestjs/common';
import { SharedModule } from '@shared/shared.module';

import { <PERSON><PERSON><PERSON><PERSON>per } from './cache.helper';
import { ConfigHelper } from './config.helper';

/**
 * Common helpers module
 * Provides shared utilities across the application
 */
@Module({
	imports: [
		SharedModule, // For RedisService
		SysConfigModule, // For SysConfigService
	],
	providers: [ConfigHelper, CacheHelper],
	exports: [ConfigHelper, CacheHelper],
})
export class HelpersModule {}
