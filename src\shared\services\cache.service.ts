import { CACHE_MANAGER } from '@nestjs/cache-manager';
import {
	Inject,
	Injectable,
	InternalServerErrorException,
} from '@nestjs/common';
import type { Cache } from 'cache-manager';

@Injectable()
export class CacheService {
	constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}

	private readonly keyPrefix = 'fsp::';

	async set(key: string, value: unknown, ttl?: number): Promise<void> {
		try {
			const prefixedKey = this.keyPrefix + key;
			await this.cacheManager.set(prefixedKey, value, ttl);
		} catch {
			throw new InternalServerErrorException();
		}
	}

	async get<T>(key: string): Promise<T> {
		const prefixedKey = this.keyPrefix + key;
		const data = await this.cacheManager.get<string>(prefixedKey);
		try {
			return JSON.parse(data as string);
		} catch {
			return data as unknown as T; // return raw string/number/etc.
		}
	}

	async del(key: string): Promise<void> {
		try {
			const prefixedKey = this.keyPrefix + key;
			await this.cacheManager.del(prefixedKey);
		} catch {
			throw new InternalServerErrorException();
		}
	}
}
