import { Injectable } from '@nestjs/common';

/**
 * Centralized Redis Key Management Service
 * Provides consistent key patterns and easy key generation across the application
 */
@Injectable()
export class RedisKeyManagerService {
	// Key patterns for different modules
	private readonly patterns = {
		// Auth module keys
		auth: {
			loginAttempts: (username: string) => `login_attempts:${username}`,
			loginAttemptsIp: (ip: string) => `login_attempts_ip:${ip}`,
			blacklistToken: (token: string) => `blacklist_token:${token}`,
			userToken: (userId: number) => `user_token:${userId}`,
		},

		// OTP module keys
		otp: {
			code: (contact: string) => `otp:${contact}`,
			rateLimit: (contact: string) => `otp_limit:${contact}`,
			rateLimitIp: (ip: string) => `otp_limit_ip:${ip}`,
			verifyAttempts: (contact: string) => `otp_verify_attempts:${contact}`,
			queue: () => 'otp:queue',
			processing: (jobId: string) => `otp:processing:${jobId}`,
			failed: (jobId: string) => `otp:failed:${jobId}`,
			metrics: {
				sent: (date: string, type: 'email' | 'zalo'): string =>
					`otp:sent:${date}:${type}`,
				verified: (date: string, type: 'email' | 'zalo'): string =>
					`otp:verified:${date}:${type}`,
			},
		},

		// User module keys
		user: {
			profile: (userId: number) => `user:${userId}`,
			cache: (userId: number) => `profile:${userId}`,
			session: (sessionId: string) => `user_session:${sessionId}`,
		},

		// Sys-Config module keys
		sysConfig: {
			value: (key: string) => `sys_config:${key}`,
			category: (category: string) => `sys_config_category:${category}`,
			stats: () => 'sys_config:cache_stats',
		},

		// General purpose keys
		cache: {
			api: (endpoint: string) => `cache:api:${endpoint}`,
			temp: (key: string) => `temp:${key}`,
			lock: (resource: string) => `lock:${resource}`,
		},
	};

	/**
	 * Get auth-related keys
	 */
	auth = {
		loginAttempts: (username: string) =>
			this.patterns.auth.loginAttempts(username),
		loginAttemptsIp: (ip: string) => this.patterns.auth.loginAttemptsIp(ip),
		blacklistToken: (token: string) => this.patterns.auth.blacklistToken(token),
		userToken: (userId: number) => this.patterns.auth.userToken(userId),
	};

	/**
	 * Get OTP-related keys
	 */
	otp = {
		code: (contact: string) => this.patterns.otp.code(contact),
		rateLimit: (contact: string) => this.patterns.otp.rateLimit(contact),
		rateLimitIp: (ip: string) => this.patterns.otp.rateLimitIp(ip),
		verifyAttempts: (contact: string) =>
			this.patterns.otp.verifyAttempts(contact),
		queue: () => this.patterns.otp.queue(),
		processing: (jobId: string) => this.patterns.otp.processing(jobId),
		failed: (jobId: string) => this.patterns.otp.failed(jobId),
		metrics: {
			sent: (date: string, type: 'email' | 'zalo') =>
				this.patterns.otp.metrics.sent(date, type),
			verified: (date: string, type: 'email' | 'zalo') =>
				this.patterns.otp.metrics.verified(date, type),
		},
	};

	/**
	 * Get user-related keys
	 */
	user = {
		profile: (userId: number) => this.patterns.user.profile(userId),
		cache: (userId: number) => this.patterns.user.cache(userId),
		session: (sessionId: string) => this.patterns.user.session(sessionId),
	};

	/**
	 * Get sys-config related keys
	 */
	sysConfig = {
		value: (key: string) => this.patterns.sysConfig.value(key),
		category: (category: string) => this.patterns.sysConfig.category(category),
		stats: () => this.patterns.sysConfig.stats(),
	};

	/**
	 * Get general cache keys
	 */
	cache = {
		api: (endpoint: string) => this.patterns.cache.api(endpoint),
		temp: (key: string) => this.patterns.cache.temp(key),
		lock: (resource: string) => this.patterns.cache.lock(resource),
	};

	/**
	 * Generate custom key with pattern validation
	 */
	custom(module: string, type: string, identifier: string): string {
		return `${module}:${type}:${identifier}`;
	}

	/**
	 * Generate time-based key (useful for metrics)
	 */
	timeBasedKey(module: string, type: string, date?: Date): string {
		const dateStr = date
			? date.toISOString().split('T')[0]
			: new Date().toISOString().split('T')[0];
		return `${module}:${type}:${dateStr}`;
	}

	/**
	 * Generate batch keys for bulk operations
	 */
	batch(
		pattern: (id: string | number) => string,
		ids: (string | number)[],
	): string[] {
		return ids.map((id) => pattern(id));
	}

	/**
	 * Get all key patterns for documentation/debugging
	 */
	getAllPatterns(): Record<string, any> {
		return this.patterns;
	}

	/**
	 * Validate key format
	 */
	validateKey(key: string): { valid: boolean; issues: string[] } {
		const issues: string[] = [];

		if (!key || key.trim().length === 0) {
			issues.push('Key cannot be empty');
		}

		if (key.includes(' ')) {
			issues.push('Key should not contain spaces');
		}

		if (key.length > 250) {
			issues.push('Key is too long (max 250 characters)');
		}

		if (!/^[a-zA-Z0-9:_-]+$/.test(key)) {
			issues.push(
				'Key contains invalid characters (only alphanumeric, :, _, - allowed)',
			);
		}

		return {
			valid: issues.length === 0,
			issues,
		};
	}

	// /**
	//  * Extract module from key
	//  */
	// extractModule(key: string): string | null {
	//     const parts = key.split(':');
	//     return parts.length > 0 ? parts[0] : null;
	// }

	// /**
	//  * Extract type from key
	//  */
	// extractType(key: string): string | null {
	//     const parts = key.split(':');
	//     return parts.length > 1 ? parts[1] : null;
	// }

	/**
	 * Extract identifier from key
	 */
	extractIdentifier(key: string): string | null {
		const parts = key.split(':');
		return parts.length > 2 ? parts.slice(2).join(':') : null;
	}
}
