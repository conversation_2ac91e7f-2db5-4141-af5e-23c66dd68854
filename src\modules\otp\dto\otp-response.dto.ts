import { ApiProperty } from '@nestjs/swagger';

export class SendOtpResponseDto {
	@ApiProperty({
		description: 'Operation status',
		example: 'success',
	})
	status!: string;

	@ApiProperty({
		description: 'Human-readable message',
		example: 'OTP sent successfully',
	})
	message!: string;

	@ApiProperty({
		description: 'Target contact (email or phone) where OTP was sent',
		example: '<EMAIL> or 84905060708',
	})
	target!: string;

	@ApiProperty({
		description: 'Detected contact type',
		enum: ['email', 'phone'],
		example: 'email',
	})
	contactType!: 'email' | 'phone';

	@ApiProperty({
		description: 'OTP expiration time in seconds',
		example: 300,
	})
	expiresIn!: number;

	@ApiProperty({
		description: 'Rate limit information',
		type: 'object',
		properties: {
			remaining: {
				type: 'number',
				description: 'Remaining attempts',
				example: 4,
			},
			resetTime: {
				type: 'number',
				description: 'Reset time in seconds',
				example: 3600,
			},
		},
	})
	rateLimit!: {
		remaining: number;
		resetTime: number;
	};
}

export class VerifyOtpResponseDto {
	@ApiProperty({
		description: 'Verification status',
		example: 'success',
	})
	status!: string;

	@ApiProperty({
		description: 'Human-readable message',
		example: 'OTP verified successfully',
	})
	message!: string;

	@ApiProperty({
		description: 'Target contact that was verified',
		example: '<EMAIL> or 84905060708',
	})
	target!: string;

	@ApiProperty({
		description: 'Verification token for next steps (if applicable)',
		example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
		required: false,
	})
	verificationToken?: string;

	@ApiProperty({
		description: 'Additional verification data',
		type: 'object',
		properties: {
			verified: {
				type: 'boolean',
				description: 'Verification status',
				example: true,
			},
			verifiedAt: {
				type: 'string',
				format: 'date-time',
				description: 'Verification timestamp',
				example: '2025-08-16T10:30:00Z',
			},
		},
	})
	data?: {
		verified: boolean;
		verifiedAt: string;
	};
}

export class OtpErrorResponseDto {
	@ApiProperty({
		description: 'Error status',
		example: 'error',
	})
	status!: string;

	@ApiProperty({
		description: 'Error message',
		example: 'Invalid OTP code',
	})
	message!: string;

	@ApiProperty({
		description: 'Error code for client handling',
		example: 'INVALID_OTP',
	})
	errorCode!: string;

	@ApiProperty({
		description: 'Additional error details',
		type: 'object',
		properties: {
			attemptsRemaining: {
				type: 'number',
				description: 'Remaining verification attempts',
				example: 2,
			},
			cooldownTime: {
				type: 'number',
				description: 'Cooldown time in seconds',
				example: 60,
			},
		},
	})
	details?: {
		attemptsRemaining?: number;
		cooldownTime?: number;
	};
}
