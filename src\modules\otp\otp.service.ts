// otp.service.ts
import { ConfigHelper } from '@common/helpers/config.helper';
import { <PERSON>rrorHelper } from '@common/helpers/error.helper';
import { isEmail } from '@common/utils';
import { SysConfigService } from '@modules/sys-config/sys-config.service';
import { UserService } from '@modules/user/user.service';
import { HttpService } from '@nestjs/axios';
import { BadRequestException, forwardRef, Inject, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { RedisService } from '@shared/services/redis.service';
import { RedisKeyManagerService } from '@shared/services/redis-key-manager.service';
import * as crypto from 'crypto';
import { firstValueFrom } from 'rxjs';

import {
	SendOtpResponseDto,
	VerifyOtpResponseDto,
} from './dto/otp-response.dto';
import { OtpMetricsService } from './otp-metrics.service';

@Injectable()
export class OtpService {
	constructor(
		private readonly redisService: RedisService,
		private readonly redisKeyManager: RedisKeyManagerService,
		private readonly httpService: HttpService,
		private readonly jwtService: JwtService,
		private readonly sysConfigService: SysConfigService,
		private readonly otpMetricsService: OtpMetricsService,
		private readonly configHelper: ConfigHelper,
		@Inject(forwardRef(() => UserService))
		private readonly userService: UserService,
	) {}

	// Default values - will be overridden by sys-config
	private readonly DEFAULT_OTP_TTL = 5; // 5 minutes
	private readonly DEFAULT_LIMIT_TTL = 5; // 5 minutes for rate limit
	private readonly DEFAULT_MAX_LIMIT_USER = 3; // 3 OTP / user / 5 minutes
	private readonly DEFAULT_MAX_LIMIT_IP = 10; // 10 OTP / IP / 5 minutes
	private readonly DEFAULT_MAX_VERIFY_ATTEMPTS = 3; // 3 verification attempts per OTP

	private MAILER_HOST = process.env.MAILER_API_HOST;
	private MAILER_API_KEY = process.env.MAILER_API_KEY;

	/**
	 * Get OTP configuration with fallback to defaults
	 */
	private async getOtpConfig<T>(key: string, defaultValue: T): Promise<T> {
		return this.configHelper.getConfigWithFallback(key, defaultValue, 'otp');
	}

	async sendOtpMail(email: string, otp: string, expireAfter: number) {
		const payload = {
			to: [email],
			template: 'otp-verification',
			context: {
				name: 'User',
				otp: otp,
				expiryMinutes: expireAfter,
				currentYear: new Date().getFullYear(),
			},
		};

		return await firstValueFrom(
			this.httpService.post(`${this.MAILER_HOST}/mail/send-template`, payload, {
				headers: {
					'x-api-key': this.MAILER_API_KEY,
					'Content-Type': 'application/json',
				},
			}),
		);
	}

	async sendOtpZalo(phone: string, otp: string, accessToken: string) {
		const payload = {
			phone: phone,
			otp: otp,
			accessToken: accessToken,
		};

		return await firstValueFrom(
			this.httpService.post(`${this.MAILER_HOST}/zalo/send-otp`, payload, {
				headers: {
					// 'x-api-key': this.MAILER_API_KEY,
					'Content-Type': 'application/json',
				},
			}),
		);
	}

	/**
	 * Generate secure 6-digit OTP
	 */
	generateOtp(): string {
		return crypto.randomInt(100000, 999999).toString();
	}

	async sendOtp(
		emailOrPhone: string,
		ipAddress: string,
	): Promise<SendOtpResponseDto> {
		const limitUserKey = this.redisKeyManager.otp.rateLimit(emailOrPhone);
		const limitIpKey = this.redisKeyManager.otp.rateLimitIp(ipAddress);
		const otpKey = this.redisKeyManager.otp.code(emailOrPhone);

		// Validate and determine contact type
		const contactType = ErrorHelper.validateContactFormat(emailOrPhone);

		// 1. Check rate limits using pipeline for better performance
		const pipeline = this.redisService.createPipeline();
		pipeline.incr(limitUserKey);
		pipeline.incr(limitIpKey);
		pipeline.ttl(limitUserKey);
		pipeline.ttl(limitIpKey);

		const results = await pipeline.exec();
		const [userCount, ipCount, userTtl, ipTtl] = results?.map(
			(r: any) => r?.[1] as number,
		) || [0, 0, -1, -1];

		// Get dynamic configuration values
		const limitTtl = await this.getOtpConfig(
			'otp_limit_ttl',
			this.DEFAULT_LIMIT_TTL,
		);
		const maxLimitUser = await this.getOtpConfig(
			'otp_max_limit_user',
			this.DEFAULT_MAX_LIMIT_USER,
		);
		const maxLimitIp = await this.getOtpConfig(
			'otp_max_limit_ip',
			this.DEFAULT_MAX_LIMIT_IP,
		);

		// Set TTL if keys are new
		if (userTtl === -1) {
			await this.redisService.expire(limitUserKey, limitTtl * 60);
		}
		if (ipTtl === -1) {
			await this.redisService.expire(limitIpKey, limitTtl * 60);
		}

		// Check limits
		if ((userCount || 0) > maxLimitUser) {
			throw new BadRequestException(
				`Too many OTP requests for ${emailOrPhone}. Please try again in ${limitTtl} minutes.`,
			);
		}
		if ((ipCount || 0) > maxLimitIp) {
			throw new BadRequestException(
				`Too many OTP requests from this IP. Please try again in ${limitTtl} minutes.`,
			);
		}

		// 2. Generate OTP
		const otp = this.generateOtp();

		// Get OTP TTL from config
		const otpTtl = await this.getOtpConfig('otp_ttl', this.DEFAULT_OTP_TTL);

		// 3. Save OTP to Redis immediately (before sending)
		await this.redisService.set(otpKey, otp, otpTtl * 60);

		// 4. Send OTP directly (synchronous)
		try {
			if (contactType === 'email') {
				await this.sendOtpMail(emailOrPhone, otp, otpTtl);
			} else {
				// For Zalo, we need access token
				const accessToken =
					await this.sysConfigService.getValue('ZALO_ACCESS_TOKEN');
				await this.sendOtpZalo(emailOrPhone, otp, accessToken);
			}

			// Record metrics
			const metricsType = contactType === 'email' ? 'email' : 'zalo';
			await this.otpMetricsService.recordOtpSent(metricsType);
		} catch (error) {
			// If sending fails, remove OTP and throw error
			await this.redisService.del(otpKey);
			throw new BadRequestException(
				`Failed to send OTP: ${error instanceof Error ? error.message : 'Unknown error'}`,
			);
		}

		// Calculate rate limit info
		const userRemaining = Math.max(0, maxLimitUser - (userCount || 0));
		const resetTime = userTtl && userTtl > 0 ? userTtl : limitTtl * 60;

		return {
			status: 'success',
			message: 'OTP sent successfully',
			target: emailOrPhone,
			contactType,
			expiresIn: otpTtl * 60, // Convert to seconds
			rateLimit: {
				remaining: userRemaining,
				resetTime,
			},
		};
	}

	async verifyOtp(
		emailOrPhone: string,
		otp: string,
	): Promise<VerifyOtpResponseDto> {
		const otpKey = this.redisKeyManager.otp.code(emailOrPhone);
		const attemptsKey = this.redisKeyManager.otp.verifyAttempts(emailOrPhone);

		// Check verification attempts rate limit
		const maxAttempts = await this.getOtpConfig(
			'otp_max_attempts',
			this.DEFAULT_MAX_VERIFY_ATTEMPTS,
		);

		const currentAttempts =
			(await this.redisService.get<number>(attemptsKey)) || 0;

		if (currentAttempts >= maxAttempts) {
			// Delete OTP when max attempts reached
			await this.redisService.del(otpKey);
			await this.redisService.del(attemptsKey);
			throw new BadRequestException(
				'Too many verification attempts. Please request a new OTP.',
			);
		}

		const storedOtp = await this.redisService.get<string>(otpKey);

		if (!storedOtp) {
			// Clean up attempts counter if OTP expired
			await this.redisService.del(attemptsKey);
			throw new BadRequestException(
				'OTP has expired. Please request a new one.',
			);
		}

		if (storedOtp !== otp) {
			// Increment verification attempts
			await this.redisService.incr(attemptsKey);
			// Set TTL for attempts counter (same as OTP TTL)
			const otpTtl = await this.getOtpConfig('otp_ttl', this.DEFAULT_OTP_TTL);
			await this.redisService.expire(attemptsKey, otpTtl * 60);

			const remainingAttempts = maxAttempts - currentAttempts - 1;
			throw new BadRequestException(
				`Invalid OTP code. ${remainingAttempts} attempts remaining.`,
			);
		}

		const type = isEmail(emailOrPhone)
			? 'email-verification'
			: 'phone-verification';

		// Tạo verificationToken (JWT chứa emailOrPhone + type)
		const verificationToken = await this.jwtService.signAsync(
			{ emailOrPhone, type },
			{ expiresIn: '15m' }, // token tự hết hạn
		);

		// Xóa OTP và attempts counter
		await this.redisService.del(otpKey);
		await this.redisService.del(attemptsKey);

		// Record metrics
		const contactType = isEmail(emailOrPhone) ? 'email' : 'zalo';
		await this.otpMetricsService.recordOtpVerified(contactType, true);

		// Update verification status in user_account table
		try {
			const verificationType = isEmail(emailOrPhone) ? 'email' : 'phone';
			await this.userService.updateVerificationStatus(emailOrPhone, verificationType);
		} catch (error) {
			// Log error but don't fail the OTP verification
			console.warn('Failed to update verification status:', error);
		}

		// Note: Worker will auto-stop when all pending OTPs are processed

		const verifiedAt = new Date().toISOString();

		return {
			status: 'success',
			message: 'OTP verified successfully',
			target: emailOrPhone,
			verificationToken,
			data: {
				verified: true,
				verifiedAt,
			},
		};
	}
}
