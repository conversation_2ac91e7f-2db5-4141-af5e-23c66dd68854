import type { OnModuleDestroy } from '@nestjs/common';
import { Injectable } from '@nestjs/common';
import Redis from 'ioredis';

import { ApiConfigService } from './api-config.service';

@Injectable()
export class RedisService implements OnModuleDestroy {
	private client!: Redis;
	private readonly keyPrefix = 'fsp::';

	constructor(private readonly configService: ApiConfigService) {
		this.initializeClient();
	}

	private initializeClient(): void {
		this.client = new Redis({
			host: this.configService.redisConfig.socket.host,
			port: this.configService.redisConfig.socket.port,
			enableReadyCheck: false,
			maxRetriesPerRequest: null,
		});

		this.client.on('error', (err: Error) => {
			console.error('Redis Client Error:', err);
		});

		this.client.on('connect', () => {
			console.info('Redis Client Connected');
		});

		this.client.on('ready', () => {
			console.info('Redis connection established successfully');
		});
	}

	private getPrefixedKey(key: string): string {
		return this.keyPrefix + key;
	}

	async set(key: string, value: unknown, ttlSeconds?: number): Promise<void> {
		try {
			const prefixedKey = this.getPrefixedKey(key);
			const serializedValue = JSON.stringify(value);

			if (ttlSeconds) {
				await this.client.setex(prefixedKey, ttlSeconds, serializedValue);
			} else {
				await this.client.set(prefixedKey, serializedValue);
			}
		} catch (error) {
			console.error('Redis SET error:', error);
			throw error;
		}
	}

	async get<T>(key: string): Promise<T | null> {
		try {
			const prefixedKey = this.getPrefixedKey(key);
			const value = await this.client.get(prefixedKey);

			if (value === null) {
				return null;
			}

			try {
				return JSON.parse(value) as T;
			} catch {
				// If JSON parsing fails, return the raw value
				return value as unknown as T;
			}
		} catch (error) {
			console.error('Redis GET error:', error);
			throw error;
		}
	}

	async del(key: string): Promise<number> {
		try {
			const prefixedKey = this.getPrefixedKey(key);
			return await this.client.del(prefixedKey);
		} catch (error) {
			console.error('Redis DEL error:', error);
			throw error;
		}
	}

	async exists(key: string): Promise<boolean> {
		try {
			const prefixedKey = this.getPrefixedKey(key);
			const result = await this.client.exists(prefixedKey);
			return result === 1;
		} catch (error) {
			console.error('Redis EXISTS error:', error);
			throw error;
		}
	}

	async keys(pattern: string = '*'): Promise<string[]> {
		try {
			const prefixedPattern = this.getPrefixedKey(pattern);
			const keys = await this.client.keys(prefixedPattern);
			// Remove prefix from returned keys
			return keys.map((key: string) => key.replace(this.keyPrefix, ''));
		} catch (error) {
			console.error('Redis KEYS error:', error);
			throw error;
		}
	}

	async flushAll(): Promise<void> {
		try {
			await this.client.flushall();
		} catch (error) {
			console.error('Redis FLUSHALL error:', error);
			throw error;
		}
	}

	async ping(): Promise<string> {
		try {
			return await this.client.ping();
		} catch (error) {
			console.error('Redis PING error:', error);
			throw error;
		}
	}

	async onModuleDestroy(): Promise<void> {
		if (this.client) {
			await this.client.quit();
			console.info('Redis client disconnected');
		}
	}

	// Additional Redis operations for OTP module
	async incr(key: string): Promise<number> {
		try {
			const prefixedKey = this.getPrefixedKey(key);
			return await this.client.incr(prefixedKey);
		} catch (error) {
			console.error('Redis INCR error:', error);
			throw error;
		}
	}

	async expire(key: string, seconds: number): Promise<boolean> {
		try {
			const prefixedKey = this.getPrefixedKey(key);
			const result = await this.client.expire(prefixedKey, seconds);
			return result === 1;
		} catch (error) {
			console.error('Redis EXPIRE error:', error);
			throw error;
		}
	}

	async ttl(key: string): Promise<number> {
		try {
			const prefixedKey = this.getPrefixedKey(key);
			return await this.client.ttl(prefixedKey);
		} catch (error) {
			console.error('Redis TTL error:', error);
			throw error;
		}
	}

	async zadd(key: string, score: number, member: string): Promise<number> {
		try {
			const prefixedKey = this.getPrefixedKey(key);
			return await this.client.zadd(prefixedKey, score, member);
		} catch (error) {
			console.error('Redis ZADD error:', error);
			throw error;
		}
	}

	async zpopmin(key: string): Promise<string[]> {
		try {
			const prefixedKey = this.getPrefixedKey(key);
			return await this.client.zpopmin(prefixedKey);
		} catch (error) {
			console.error('Redis ZPOPMIN error:', error);
			throw error;
		}
	}

	async zcard(key: string): Promise<number> {
		try {
			const prefixedKey = this.getPrefixedKey(key);
			return await this.client.zcard(prefixedKey);
		} catch (error) {
			console.error('Redis ZCARD error:', error);
			throw error;
		}
	}

	// Pipeline operations
	createPipeline() {
		return new RedisPipeline(this.client.pipeline(), this.keyPrefix);
	}

	// Getter for direct client access if needed (for complex operations)
	getClient(): Redis {
		return this.client;
	}
}

// Pipeline wrapper to handle key prefixing
class RedisPipeline {
	constructor(
		private pipeline: any,
		private keyPrefix: string,
	) {}

	private getPrefixedKey(key: string): string {
		return this.keyPrefix + key;
	}

	incr(key: string) {
		this.pipeline.incr(this.getPrefixedKey(key));
		return this;
	}

	expire(key: string, seconds: number) {
		this.pipeline.expire(this.getPrefixedKey(key), seconds);
		return this;
	}

	ttl(key: string) {
		this.pipeline.ttl(this.getPrefixedKey(key));
		return this;
	}

	del(key: string) {
		this.pipeline.del(this.getPrefixedKey(key));
		return this;
	}

	async exec() {
		return await this.pipeline.exec();
	}
}
