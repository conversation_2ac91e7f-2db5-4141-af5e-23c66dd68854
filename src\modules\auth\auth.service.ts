import { ConfigHelper } from '@common/helpers/config.helper';
import { isEmail, isVietnamesePhone, validateHash } from '@common/utils';
import { RoleType } from '@constants/role-type';
import { TokenType } from '@constants/token-type';
import { UserAccountStatus, UserAccountType } from '@constants/user';
import { UserService } from '@modules/user/user.service';
import { UserAccountEntity } from '@modules/user/user-account.entity';
import {
	BadRequestException,
	ConflictException,
	ForbiddenException,
	Injectable,
	UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ApiConfigService } from '@shared/services/api-config.service';
import { RedisService } from '@shared/services/redis.service';
import { RedisKeyManagerService } from '@shared/services/redis-key-manager.service';
import { SecurityLoggerService } from '@shared/services/security-logger.service';
import type { Request } from 'express';

import { UserNotFoundException } from '../../exceptions/user-not-found.exception';
import { OtpService } from '../otp/otp.service.ts';
import {
	ContactVerifyDataDto,
	LoginDataDto,
	LogoutDataDto,
	RefreshTokenDataDto,
	RegisterDataDto,
	ResetPasswordDataDto,
	ResetPasswordRequestDataDto,
} from './dto/auth-response.dto';
import type { ChangePasswordDto } from './dto/change-password.dto';
import type { RefreshTokenDto } from './dto/refresh-token.dto.ts';
import type { ResetPasswordDto, ResetPasswordRequestDto } from './dto/reset-password.dto.ts';
import type { SocialInfoDto } from './dto/social-info.dto.ts';
import { TokenPayloadDto } from './dto/token-payload.dto.ts';
import type { UserLoginDto } from './dto/user-login.dto.ts';
import type { UserRegisterDto } from './dto/user-register.dto.ts';

@Injectable()
export class AuthService {
	constructor(
		private jwtService: JwtService,
		private configService: ApiConfigService,
		private userService: UserService,
		private otpService: OtpService,
		private redisService: RedisService,
		private redisKeyManager: RedisKeyManagerService,
		private securityLogger: SecurityLoggerService,
		private configHelper: ConfigHelper,
	) {}

	async login(userLoginDto: UserLoginDto, ip: string, isSSO = false): Promise<LoginDataDto> {
		const userAccountEntity = await this.validateUser(userLoginDto, ip, isSSO);

		const token = await this.createToken({
			userId: userAccountEntity.userId,
		});

		await this.userService.updateLastLoginInfo(userAccountEntity.userId, ip);
		await this.userService.updateRefreshToken(
			userAccountEntity.userId,
			token.refreshToken,
		);

		return this.getLoginData(userAccountEntity, token, ip);
	}

	getLoginData(
		user: UserAccountEntity,
		tokens: TokenPayloadDto,
		ip: string,
	): LoginDataDto {
		return {
			user: user.toDto(),
			tokens,
			metadata: {
				loginAt: new Date().toISOString(),
				ipAddress: ip,
			},
		};
	}

	async register(
		userRegisterDto: UserRegisterDto,
		ip: string,
	): Promise<RegisterDataDto> {
		const { identifier, verificationToken } = userRegisterDto;

		let payload: { emailOrPhone: string; type: string };
		try {
			payload = await this.jwtService.verifyAsync(verificationToken);
		} catch {
			throw new ForbiddenException('Invalid or expired verification token');
		}

		if (payload.emailOrPhone !== identifier) {
			throw new ForbiddenException(
				'Verification token does not match the provided contact information',
			);
		}

		const isEmailType = isEmail(identifier);
		const existingUser = await this.userService.findOne({
			[isEmailType ? 'email' : 'phone']: identifier,
		});

		if (existingUser) {
			throw new ConflictException('User with this email/phone already exists');
		}

		const createdUser = await this.userService.createUser(userRegisterDto, ip);
		const tokens = await this.createToken({ userId: createdUser.userId });

		this.userService.updateLastLoginInfo(createdUser.userId, ip);
		this.userService.updateRefreshToken(createdUser.userId, tokens.refreshToken);

		return {
			user: createdUser.toDto(),
			tokens,
			metadata: {
				registeredAt: new Date().toISOString(),
				registrationMethod: isEmailType ? 'email_otp' : 'phone_otp',
				ipAddress: ip,
			},
		};
	}

	async refreshToken(
		refreshTokenDto: RefreshTokenDto,
	): Promise<RefreshTokenDataDto> {
		const { refreshToken } = refreshTokenDto;

		if (!refreshToken) {
			throw new BadRequestException('Refresh token must be provided');
		}

		const userAccountEntity = await this.getUserFromRefreshToken(refreshToken);

		if (!userAccountEntity) {
			throw new UnauthorizedException('Invalid or expired refresh token');
		}

		const newTokens = await this.createToken({ userId: userAccountEntity.userId });
		await this.userService.updateRefreshToken(
			userAccountEntity.userId,
			newTokens.refreshToken,
		);

		return {
			tokens: newTokens,
			metadata: {
				refreshedAt: new Date().toISOString(),
				expiresAt: new Date(
					Date.now() + newTokens.expiresIn * 1000,
				).toISOString(),
			},
		};
	}

	async logout(
		user: UserAccountEntity,
		request: Request,
	): Promise<LogoutDataDto> {
		const authHeader = request.headers.authorization;
		if (authHeader?.startsWith('Bearer ')) {
			const token = authHeader.substring(7);
			await this.blacklistToken(token);
		}

		await this.userService.updateRefreshToken(user.userId, null);
		await this.clearUserSession(user.userId);

		// console.info(`User ${user.username} logged out`);

		return {
			metadata: {
				logoutAt: new Date().toISOString(),
			},
		};
	}

	async verifyContact<T extends { contact: string }>(
		dto: T,
		ip: string,
	): Promise<ContactVerifyDataDto> {
		const { contact } = dto;
		if (!contact) {
			throw new BadRequestException('Contact is required.');
		}

		const isEmailContact = isEmail(contact);
		const isPhoneContact = isVietnamesePhone(contact);

		if (!isEmailContact && !isPhoneContact) {
			throw new BadRequestException(
				'Must be a valid email address or Vietnamese phone number (84xxxxxxxxx)',
			);
		}

		const contactType = isEmailContact ? 'email' : 'phone';
		const user = await this.userService.findOne({ [contactType]: contact });

		if (user) {
			if (user.status === UserAccountStatus.ACTIVE) {
				return {
					status: 'LOGIN_REQUIRED',
					contactType,
					message: 'User exists. Please login with your password.',
					details: {
						userExists: true,
						accountStatus: user.status,
					},
				};
			}
			throw new ForbiddenException(
				'Account exists but is not active. Please contact support.',
			);
		}

		await this.otpService.sendOtp(contact, ip);

		return {
			status: 'OTP_SENT',
			contactType,
			message: `OTP sent to your ${contactType} for registration.`,
			details: {
				userExists: false,
			},
		};
	}

	async requestPasswordReset(
		resetPasswordRequestDto: ResetPasswordRequestDto,
		ip: string,
	): Promise<ResetPasswordRequestDataDto> {
		const { emailOrPhone } = resetPasswordRequestDto;
		const isEmailContact = isEmail(emailOrPhone);

		if (!isEmailContact && !isVietnamesePhone(emailOrPhone)) {
			throw new BadRequestException(
				'Must be a valid email address or Vietnamese phone number (84xxxxxxxxx)',
			);
		}

		const user = await this.userService.findOne({
			[isEmailContact ? 'email' : 'phone']: emailOrPhone,
		});

		if (!user || user.status !== UserAccountStatus.ACTIVE) {
			// Don't reveal if user exists or not for security, but proceed as if successful
			return {
				target: emailOrPhone,
				expiresIn: 300, // 5 minutes
			};
		}

		await this.otpService.sendOtp(emailOrPhone, ip);

		return {
			target: emailOrPhone,
			expiresIn: 300, // 5 minutes
		};
	}

	async changePassword(
		user: UserAccountEntity,
		changePasswordDto: ChangePasswordDto,
	): Promise<ResetPasswordDataDto> {
		const { currentPassword, newPassword } = changePasswordDto;

		// Validate current password
		const isPasswordValid = await validateHash(
			currentPassword,
			user.passwordHash,
		);
		if (!isPasswordValid) {
			throw new BadRequestException('Current password is incorrect');
		}

		// Update to new password
		await this.userService.updatePassword(user.userId, newPassword);
		await this.clearUserSession(user.userId);

		return {
			metadata: {
				resetAt: new Date().toISOString(),
				method: 'change_password',
			},
		};
	}

	async resetPassword(
		resetPasswordDto: ResetPasswordDto,
	): Promise<ResetPasswordDataDto> {
		const { emailOrPhone, verificationToken, newPassword } = resetPasswordDto;

		let payload: any;
		try {
			payload = await this.jwtService.verifyAsync(verificationToken);
			if (payload.emailOrPhone !== emailOrPhone) {
				throw new BadRequestException(
					'Verification token does not match email/phone',
				);
			}
		} catch (error) {
			console.error(error);
			throw new BadRequestException('Invalid or expired verification token');
		}

		const user = await this.userService.findOne({
			[isEmail(emailOrPhone) ? 'email' : 'phone']: emailOrPhone,
		});

		if (!user || user.status !== UserAccountStatus.ACTIVE) {
			throw new BadRequestException('User account not found or not active');
		}

		await this.userService.updatePassword(user.userId, newPassword);
		await this.clearUserSession(user.userId);

		return {
			metadata: {
				resetAt: new Date().toISOString(),
				method: 'otp_verification',
			},
		};
	}

	// --- Private and Utility Methods ---

	private async getAuthConfig<T>(key: string, defaultValue: T): Promise<T> {
		return this.configHelper.getConfigWithFallback(key, defaultValue, 'auth');
	}

	async invalidateUserCache(userId: number): Promise<void> {
		const cacheKey = this.redisKeyManager.auth.userToken(userId);
		try {
			await this.redisService.del(cacheKey);
		} catch (_error) {
			// Log error but don't fail the operation
			console.error(_error);
		}
	}

	async createToken(data: { userId: number }): Promise<TokenPayloadDto> {
		const [token, refreshToken] = await Promise.all([
			this.jwtService.signAsync(
				{ userId: data.userId, type: TokenType.ACCESS_TOKEN, role: RoleType.USER },
				{ expiresIn: this.configService.authConfig.jwtExpirationTime },
			),
			this.jwtService.signAsync(
				{ userId: data.userId, type: TokenType.REFRESH_TOKEN, role: RoleType.USER },
				{ expiresIn: this.configService.authConfig.jwtRefreshTokenExpirationTime },
			),
		]);

		return new TokenPayloadDto({
			userId: data.userId,
			expiresIn: this.configService.authConfig.jwtExpirationTime,
			token,
			refreshToken,
		});
	}

	async createSsoToken(data: { userId: number }): Promise<string> {
		return this.jwtService.signAsync(
			{ userId: data.userId, type: TokenType.SSO_TOKEN, role: RoleType.SSO_USER },
			{ expiresIn: '60s' },
		);
	}

	async validateUser(
		userLoginDto: UserLoginDto,
		ip: string,
		isSSO = false,
	): Promise<UserAccountEntity> {
		await this.checkLoginRateLimit(userLoginDto.username, ip);

		const user = await this.userService.findByUsernameOrEmail({
			username: userLoginDto.username,
			email: null,
		});

		if (!user || user.status !== UserAccountStatus.ACTIVE) {
			await this.recordFailedLogin(userLoginDto.username, ip);
			throw new UserNotFoundException();
		}

		if (!isSSO) {
			const isPasswordValid = await validateHash(
				userLoginDto.password,
				user.passwordHash,
			);
			if (!isPasswordValid) {
				await this.recordFailedLogin(userLoginDto.username, ip);
				await this.securityLogger.logSecurityEvent({
					type: 'LOGIN_FAILED',
					userId: user.userId,
					username: userLoginDto.username,
					ip,
					timestamp: new Date(),
					details: { reason: 'Invalid password' },
				});
				throw new UserNotFoundException();
			}
		}

		await this.clearFailedLogins(userLoginDto.username, ip);
		await this.securityLogger.logSecurityEvent({
			type: 'LOGIN_SUCCESS',
			userId: user.userId,
			username: user.username || undefined,
			ip,
			timestamp: new Date(),
		});

		return user;
	}

	private async checkLoginRateLimit(username: string, ip: string): Promise<void> {
		const [maxAttempts, ipMaxAttempts, userAttempts, ipAttempts] = await Promise.all([
			this.getAuthConfig('max_login_attempts', 5),
			this.getAuthConfig('max_ip_login_attempts', 20),
			this.redisService.get<number>(this.redisKeyManager.auth.loginAttempts(username)),
			this.redisService.get<number>(this.redisKeyManager.auth.loginAttemptsIp(ip)),
		]);

		if (userAttempts && userAttempts >= maxAttempts) {
			throw new BadRequestException('Too many login attempts for this account.');
		}
		if (ipAttempts && ipAttempts >= ipMaxAttempts) {
			throw new BadRequestException('Too many login attempts from this IP.');
		}
	}

	private async recordFailedLogin(username: string, ip: string): Promise<void> {
		const ttl = await this.getAuthConfig('login_attempt_ttl', 900);
		const pipeline = this.redisService.createPipeline();
		pipeline.incr(this.redisKeyManager.auth.loginAttempts(username));
		pipeline.expire(this.redisKeyManager.auth.loginAttempts(username), ttl);
		pipeline.incr(this.redisKeyManager.auth.loginAttemptsIp(ip));
		pipeline.expire(this.redisKeyManager.auth.loginAttemptsIp(ip), ttl);
		await pipeline.exec();
	}

	private async clearFailedLogins(username: string, ip: string): Promise<void> {
		const pipeline = this.redisService.createPipeline();
		pipeline.del(this.redisKeyManager.auth.loginAttempts(username));
		pipeline.del(this.redisKeyManager.auth.loginAttemptsIp(ip));
		await pipeline.exec();
	}

	async getUserFromToken(token: string): Promise<UserAccountEntity> {
		const payload = await this.jwtService.verifyAsync(token, {
			secret: this.configService.authConfig.publicKey,
		});

		if (payload.type !== TokenType.ACCESS_TOKEN) {
			throw new UserNotFoundException();
		}

		const user = await this.userService.findOne({ userId: payload.userId });

		if (!user) {
			throw new UserNotFoundException();
		}

		return user;
	}

	async getUserFromRefreshToken(token: string): Promise<UserAccountEntity> {
		const payload = await this.jwtService.verifyAsync(token, {
			secret: this.configService.authConfig.publicKey,
		});

		if (payload.type !== TokenType.REFRESH_TOKEN) {
			throw new UserNotFoundException();
		}

		const user = await this.userService.findOne({ userId: payload.userId });

		if (!user || user.refreshToken !== token) {
			throw new UnauthorizedException('Invalid refresh token');
		}

		return user;
	}

	async loginWithOAuth(
		socialInfo: SocialInfoDto,
		ip: string,
	): Promise<{ ssoToken?: string | null; status: UserAccountStatus }> {
		const { email, socialUid, provider } = socialInfo;
		let matchedUser: UserAccountEntity | null = null;

		if (socialUid) {
			matchedUser = await this.userService.findOne({
				socialUid,
				accountType: provider,
			});
		}

		if (!matchedUser && email) {
			const userByEmail = await this.userService.findOne({ email });
			if (userByEmail) {
				if (userByEmail.accountType === UserAccountType.QUICKPLAY) {
					throw new ForbiddenException('Cannot link Quickplay account.');
				}
				if (userByEmail.socialUid) {
					throw new ConflictException('Account already linked to another social provider.');
				}
				matchedUser = await this.userService.linkUserSSO(userByEmail, socialInfo);
			}
		}

		if (!matchedUser) {
			matchedUser = await this.userService.createUserSSO(socialInfo, ip);
		}

		if (matchedUser.status !== UserAccountStatus.ACTIVE) {
			return { ssoToken: null, status: matchedUser.status };
		}

		const ssoToken = await this.createSsoToken({ userId: matchedUser.userId });
		return { ssoToken, status: matchedUser.status };
	}

	async blacklistToken(token: string): Promise<void> {
		try {
			const payload = await this.jwtService.verifyAsync(token, {
				secret: this.configService.authConfig.publicKey,
			});
			const ttl = payload.exp - Math.floor(Date.now() / 1000);
			if (ttl > 0) {
				await this.redisService.set(
					this.redisKeyManager.auth.blacklistToken(token),
					'true',
					ttl,
				);
			}
		} catch {
			// Invalid token, no need to blacklist
		}
	}

	async isTokenBlacklisted(token: string): Promise<boolean> {
		const key = this.redisKeyManager.auth.blacklistToken(token);
		return (await this.redisService.get<string>(key)) === 'true';
	}

	async cacheUserSession(userId: number, sessionData: any): Promise<void> {
		const key = this.redisKeyManager.user.session(userId.toString());
		await this.redisService.set(key, sessionData, 3600);
	}

	async getUserSession(userId: number): Promise<any | null> {
		const key = this.redisKeyManager.user.session(userId.toString());
		return this.redisService.get(key);
	}

	async clearUserSession(userId: number): Promise<void> {
		const key = this.redisKeyManager.user.session(userId.toString());
		await this.redisService.del(key);
	}
}