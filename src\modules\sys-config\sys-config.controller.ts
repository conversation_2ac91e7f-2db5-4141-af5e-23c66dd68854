import {
	Body,
	Controller,
	Delete,
	Get,
	HttpCode,
	HttpStatus,
	Param,
	Post,
	Query,
} from '@nestjs/common';
import {
	ApiBadRequestResponse,
	ApiBearerAuth,
	ApiBody,
	ApiForbiddenResponse,
	ApiNotFoundResponse,
	ApiOkResponse,
	ApiOperation,
	ApiParam,
	ApiQuery,
	ApiTags,
	ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { RoleType } from '../../constants/role-type';
import { Auth } from '../../decorators/http.decorators';
import {
	ConfigCacheStatsDto,
	GetConfigValueDto,
	SetConfigValueDto,
	SysConfigDto,
} from './dto/sys-config.dto';
import { SysConfigService } from './sys-config.service';

@ApiTags('System Configuration (Admin Only)')
@ApiBearerAuth()
@Controller('sys-config')
export class SysConfigController {
	constructor(private readonly sysConfigService: SysConfigService) {}

	@Get('stats/cache')
	@Auth([RoleType.ADMIN])
	@ApiOperation({
		summary: 'Get cache statistics (Admin Only)',
		description:
			'Get detailed cache performance statistics for system configurations. Requires admin authentication.',
	})
	@ApiOkResponse({
		description: 'Cache statistics retrieved successfully',
		type: ConfigCacheStatsDto,
		schema: {
			example: {
				totalCached: 150,
				hitCount: 1250,
				missCount: 50,
				hitRatio: 96.15,
			},
		},
	})
	@ApiUnauthorizedResponse({
		description: 'Authentication required',
	})
	@ApiForbiddenResponse({
		description: 'Admin role required',
	})
	async getCacheStats(): Promise<ConfigCacheStatsDto> {
		return this.sysConfigService.getCacheStats();
	}

	@Post('cache/clear')
	@Auth([RoleType.ADMIN])
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: 'Clear configuration cache (Admin Only)',
		description:
			'Clear all cached configuration values. Use with caution as it may impact performance temporarily. Requires admin authentication.',
	})
	@ApiOkResponse({
		description: 'Cache cleared successfully',
		schema: {
			example: {
				status: 'success',
				message: 'Configuration cache cleared successfully',
			},
		},
	})
	async clearCache(): Promise<{ status: string; message: string }> {
		await this.sysConfigService.clearCache();
		return {
			status: 'success',
			message: 'Configuration cache cleared successfully',
		};
	}

	@Post('cache/preload')
	@Auth([RoleType.ADMIN])
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: 'Preload configuration cache (Admin Only)',
		description:
			'Load all configurations into cache for better performance. Requires admin authentication.',
	})
	@ApiOkResponse({
		description: 'Cache preloaded successfully',
		schema: {
			example: {
				status: 'success',
				message: 'Configuration cache preloaded successfully',
			},
		},
	})
	async preloadCache(): Promise<{ status: string; message: string }> {
		await this.sysConfigService.preloadCache();
		return {
			status: 'success',
			message: 'Configuration cache preloaded successfully',
		};
	}

	@Get('category/:category')
	@Auth([RoleType.ADMIN])
	@ApiOperation({
		summary: 'Get configurations by category (Admin Only)',
		description:
			'Retrieve all configuration values for a specific category. Requires admin authentication.',
	})
	@ApiParam({
		name: 'category',
		description: 'Configuration category',
		example: 'general',
	})
	@ApiOkResponse({
		description: 'Configurations retrieved successfully',
		type: [SysConfigDto],
	})
	@ApiNotFoundResponse({
		description: 'No configurations found for the specified category',
	})
	async getByCategory(
		@Param('category') category: string,
	): Promise<SysConfigDto[]> {
		const configs = await this.sysConfigService.getByCategory(category);
		return configs.map((config) => config.toDto());
	}

	@Get(':key')
	@Auth([RoleType.ADMIN])
	@ApiOperation({
		summary: 'Get configuration value by key (Admin Only)',
		description:
			'Retrieve a specific configuration value. Secret values will be masked. Requires admin authentication.',
	})
	@ApiParam({
		name: 'key',
		description: 'Configuration key',
		example: 'site_name',
	})
	@ApiOkResponse({
		description: 'Configuration value retrieved successfully',
		type: GetConfigValueDto,
		schema: {
			example: {
				key: 'site_name',
				value: 'My Website',
				dataType: 'string',
			},
		},
	})
	@ApiNotFoundResponse({
		description: 'Configuration key not found',
		schema: {
			example: {
				statusCode: 404,
				message: 'Configuration key "invalid_key" not found',
				error: 'Not Found',
			},
		},
	})
	async getValue(@Param('key') key: string): Promise<GetConfigValueDto> {
		return this.sysConfigService.getConfigForApi(key);
	}

	@Get()
	@Auth([RoleType.ADMIN])
	@ApiOperation({
		summary: 'Get all configurations (Admin Only)',
		description:
			'Retrieve all configuration values with optional filtering. Requires admin authentication.',
	})
	@ApiQuery({
		name: 'category',
		required: false,
		description: 'Filter by category',
		example: 'general',
	})
	@ApiOkResponse({
		description: 'Configurations retrieved successfully',
		type: [SysConfigDto],
	})
	async findAll(@Query('category') category?: string): Promise<SysConfigDto[]> {
		const configs = await this.sysConfigService.findAll(category);
		return configs.map((config) => config.toDto());
	}

	@Post()
	@Auth([RoleType.ADMIN])
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: 'Set configuration value',
		description:
			'Create or update a configuration value with type validation and caching',
	})
	@ApiBody({ type: SetConfigValueDto })
	@ApiOkResponse({
		description: 'Configuration set successfully',
		type: SysConfigDto,
		schema: {
			example: {
				key: 'site_name',
				value: 'My Website',
				description: 'Website name displayed in the header',
				dataType: 'string',
				category: 'general',
				createdAt: '2025-08-16T10:30:00Z',
				updatedAt: '2025-08-16T10:30:00Z',
				typedValue: 'My Website',
			},
		},
	})
	@ApiBadRequestResponse({
		description: 'Invalid input data or type validation failed',
		schema: {
			examples: {
				invalidType: {
					summary: 'Invalid data type',
					value: {
						statusCode: 400,
						message: 'Value "abc" is not a valid number',
						error: 'Bad Request',
					},
				},
				invalidJson: {
					summary: 'Invalid JSON',
					value: {
						statusCode: 400,
						message: 'Value "{invalid json" is not valid JSON',
						error: 'Bad Request',
					},
				},
			},
		},
	})
	async setValue(@Body() dto: SetConfigValueDto): Promise<SysConfigDto> {
		const config = await this.sysConfigService.setValue(dto);
		return config.toDto();
	}

	@Post('bulk')
	@Auth([RoleType.ADMIN])
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: 'Bulk set configurations',
		description: 'Set multiple configuration values in a single request',
	})
	@ApiBody({
		type: [SetConfigValueDto],
		description: 'Array of configuration values to set',
	})
	@ApiOkResponse({
		description: 'Configurations set successfully',
		type: [SysConfigDto],
	})
	async bulkSet(@Body() configs: SetConfigValueDto[]): Promise<SysConfigDto[]> {
		const results = await this.sysConfigService.bulkSet(configs);
		return results.map((config) => config.toDto());
	}

	@Delete(':key')
	@Auth([RoleType.ADMIN])
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: 'Delete configuration',
		description: 'Delete a configuration value and clear its cache',
	})
	@ApiParam({
		name: 'key',
		description: 'Configuration key to delete',
		example: 'old_setting',
	})
	@ApiOkResponse({
		description: 'Configuration deleted successfully',
		schema: {
			example: {
				status: 'success',
				message: 'Configuration deleted successfully',
			},
		},
	})
	@ApiNotFoundResponse({
		description: 'Configuration key not found',
	})
	async deleteConfig(
		@Param('key') key: string,
	): Promise<{ status: string; message: string }> {
		await this.sysConfigService.deleteConfig(key);
		return {
			status: 'success',
			message: 'Configuration deleted successfully',
		};
	}
}
