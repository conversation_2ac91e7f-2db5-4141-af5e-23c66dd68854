import { Injectable } from '@nestjs/common';
import { RedisService } from '@shared/services/redis.service';
import { RedisKeyManagerService } from '@shared/services/redis-key-manager.service';

@Injectable()
export class OtpMetricsService {
	constructor(
		private readonly redisService: RedisService,
		private readonly redisKeyManager: RedisKeyManagerService,
	) {}

	/**
	 * Record OTP sent event (simple counter)
	 */
	async recordOtpSent(type: 'email' | 'zalo') {
		const today = new Date().toISOString().split('T')[0]!;

		try {
			const key = this.redisKeyManager.otp.metrics.sent(today, type);
			await this.redisService.incr(key);
			await this.redisService.expire(key, 86400 * 7); // Keep for 7 days
		} catch (_error) {
			// Ignore metrics errors
		}
	}

	/**
	 * Record OTP verification event (simple counter)
	 */
	async recordOtpVerified(type: 'email' | 'zalo', success: boolean) {
		const today = new Date().toISOString().split('T')[0]!;

		try {
			if (success) {
				const key = this.redisKeyManager.otp.metrics.verified(today, type);
				await this.redisService.incr(key);
				await this.redisService.expire(key, 86400 * 7);
			}
		} catch (_error) {
			// Ignore metrics errors
		}
	}

	/**
	 * Get simple daily stats
	 */
	async getDailyStats(date: string) {
		try {
			const [emailSent, zaloSent, emailVerified, zaloVerified] =
				await Promise.all([
					this.redisService.get<string>(
						this.redisKeyManager.otp.metrics.sent(date, 'email'),
					),
					this.redisService.get<string>(
						this.redisKeyManager.otp.metrics.sent(date, 'zalo'),
					),
					this.redisService.get<string>(
						this.redisKeyManager.otp.metrics.verified(date, 'email'),
					),
					this.redisService.get<string>(
						this.redisKeyManager.otp.metrics.verified(date, 'zalo'),
					),
				]);

			return {
				sent: {
					email: parseInt(emailSent || '0'),
					zalo: parseInt(zaloSent || '0'),
				},
				verified: {
					email: parseInt(emailVerified || '0'),
					zalo: parseInt(zaloVerified || '0'),
				},
			};
		} catch (_error) {
			return {
				sent: { email: 0, zalo: 0 },
				verified: { email: 0, zalo: 0 },
			};
		}
	}
}
