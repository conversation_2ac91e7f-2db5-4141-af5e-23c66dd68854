import { PageDto } from '@common/dto/page.dto';
// import { ResponseDto } from '@common/dto/response.dto.ts';
import { CacheHelper } from '@common/helpers/cache.helper';
import { isEmail } from '@common/utils.ts';
import { PaymentMethod, PaymentStatus } from '@constants/payment';
import { SocialCode, UserAccountType } from '@constants/user';
import { SocialInfoDto } from '@modules/auth/dto/social-info.dto.ts';
import { TransactionHistoryDto } from '@modules/payment/dtos/transaction-history.dto';
import type { TransactionHistoryOptionsDto } from '@modules/payment/dtos/transaction-history-options.dto';
import { PaymentTransactionEntity } from '@modules/payment/payment-transaction.entity';
import {
	BadRequestException,
	ConflictException,
	Injectable,
	NotFoundException,
} from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { InjectRepository } from '@nestjs/typeorm';
import { ApiConfigService } from '@shared/services/api-config.service';
import { RedisService } from '@shared/services/redis.service';
import { RedisKeyManagerService } from '@shared/services/redis-key-manager.service';
import { plainToClass } from 'class-transformer';
import { GeneratorProvider } from 'providers/generator.provider.ts';
import type { FindManyOptions, FindOptionsWhere } from 'typeorm';
import { Repository } from 'typeorm';
import { Transactional } from 'typeorm-transactional';

import { UserNotFoundException } from '../../exceptions/user-not-found.exception.ts';
import { UserRegisterDto } from '../auth/dto/user-register.dto.ts';
import { UserRegisterSsoDto } from '../auth/dto/user-register-sso.dto.ts';
import { CreateUserProfileCommand } from './commands/create-user-profile.command.ts';
import { MutateUserProfileDto } from './dtos/mutate-user-profile.dto.ts';
import type { UserQuickplayLinkDto } from './dtos/user-quickplay-link.dto.ts';
import type { UserQuickplayLoginDto } from './dtos/user-quickplay-login.dto.ts';
import { UserQuickplayResponseDto } from './dtos/user-quickplay-response.dto.ts';
import { UserProfileNotFoundException } from './exceptions/user-profile-not-found.exception.ts';
import { UserAccountEntity } from './user-account.entity.ts';
import { UserProfileEntity } from './user-profile.entity.ts';

@Injectable()
export class UserService {
	constructor(
		@InjectRepository(UserAccountEntity)
		private userRepository: Repository<UserAccountEntity>,
		@InjectRepository(UserProfileEntity)
		private userProfileRepository: Repository<UserProfileEntity>,
		@InjectRepository(PaymentTransactionEntity)
		private paymentTransactionRepository: Repository<PaymentTransactionEntity>,
		// private validatorService: ValidatorService,
		private commandBus: CommandBus,
		private configService: ApiConfigService,
		private redisService: RedisService,
		private redisKeyManager: RedisKeyManagerService,
		private cacheHelper: CacheHelper,
	) {}

	findOne(
		findData: FindOptionsWhere<UserAccountEntity>,
	): Promise<UserAccountEntity | null> {
		return this.userRepository.findOneBy(findData);
	}

	find(
		findData: FindManyOptions<UserAccountEntity>,
	): Promise<UserAccountEntity[]> {
		return this.userRepository.find(findData);
	}

	findByUsernameOrEmail(
		options: Partial<{ username?: string | null; email?: string | null }>,
	): Promise<UserAccountEntity | null> {
		const queryBuilder = this.userRepository.createQueryBuilder('user');

		if (options.email) {
			queryBuilder.orWhere('user.email = :email', {
				email: options.email,
			});
		}

		if (options.username) {
			queryBuilder.orWhere('user.username = :username', {
				username: options.username,
			});
		}

		return queryBuilder.getOne();
	}

	@Transactional()
	async createUser(
		userRegisterDto: UserRegisterDto,
		ip: string,
	): Promise<UserAccountEntity> {
		const { identifier, password } = userRegisterDto;
		const user = this.userRepository.create();

		if (!identifier) {
			throw new BadRequestException('Identifier is required.');
		}

		user.username = identifier;

		if (isEmail(identifier)) {
			user.email = identifier;
		} else {
			user.phone = identifier;
		}

		user.accountType = UserAccountType.LOCAL;
		user.passwordHash = password;
		user.createdAtIp = ip;

		// Set verification status based on registration method
		if (isEmail(identifier)) {
			user.isEmailVerified = true; // Email was verified via OTP
			user.isPhoneVerified = false;
		} else {
			user.isEmailVerified = false;
			user.isPhoneVerified = true; // Phone was verified via OTP
		}

		await this.userRepository.save(user);

		user.userProfile = await this.createUserProfile(
			user.userId,
			plainToClass(MutateUserProfileDto, {
				displayName: user.username, // Default display name to username
				gender: null,
				avatarUrl: null,
				dob: null,
				address: null,
				identifierNumber: null,
			}),
		);

		// Verification status already set above

		return user;
	}

	@Transactional()
	async createUserSSO(
		socialInfo: SocialInfoDto,
		ip: string,
	): Promise<UserAccountEntity> {
		const userRegisterSsoDto = new UserRegisterSsoDto({
			username: `${SocialCode.get(socialInfo.provider)}_${socialInfo.socialUid}`, // Use email as username for SSO users
			email: socialInfo.email || null, // SSO users may not have an email
			accountType: socialInfo.provider, // Use the provider as the account type
			socialUid: socialInfo.socialUid, // This can be set later based on the SSO provider
			linkedAt: new Date(),
			socialAccessToken: socialInfo.accessToken,
			socialRefreshToken: socialInfo.refreshToken,
			createdAtIp: ip,
		});

		const user = this.userRepository.create(userRegisterSsoDto);

		// Set verification status for SSO users
		user.isEmailVerified = !!socialInfo.email; // Email verified if exists
		user.isPhoneVerified = false; // SSO users don't have phone verified

		await this.userRepository.save(user);

		user.userProfile = await this.createUserProfile(
			user.userId,
			plainToClass(MutateUserProfileDto, {
				displayName: socialInfo.name,
				gender: null,
				avatarUrl: socialInfo.avatarUrl,
				dob: null,
				address: null,
				identifierNumber: null,
			}),
		);

		// Verification status already set above

		return user;
	}

	@Transactional()
	async linkUserSSO(
		userAccount: UserAccountEntity,
		socialInfo: SocialInfoDto,
	): Promise<UserAccountEntity> {
		userAccount.socialUid = socialInfo.socialUid;
		userAccount.accountType = socialInfo.provider;
		userAccount.linkedAt = new Date();
		userAccount.socialAccessToken = socialInfo.accessToken ?? null;
		userAccount.socialRefreshToken = socialInfo.refreshToken ?? null;

		return await this.userRepository.save(userAccount);
	}

	@Transactional()
	async createUserQuickplay(
		userQuickplayDto: UserQuickplayLoginDto,
		ip: string,
	): Promise<UserQuickplayResponseDto> {
		const username = `${SocialCode.get(UserAccountType.QUICKPLAY)}_${GeneratorProvider.uuid()}`;

		const user = this.userRepository.create({
			username,
			email: null,
			accountType: UserAccountType.QUICKPLAY,
			createdAtIp: ip,
		});

		// Hash pseudo-password để tránh bị lộ
		const rawPassword = `${userQuickplayDto.platform}-${userQuickplayDto.uniqueId}-${userQuickplayDto.deviceId}`;
		user.passwordHash = rawPassword;

		const savedUser = await this.userRepository.save(user);

		// Tạo profile mặc định nếu chưa có (quickplay chưa cần)
		// await this.createUserProfile(
		//   savedUser.userId,
		//   plainToClass(MutateUserProfileDto, {
		//     displayName: null,
		//     gender: null,
		//     avatarUrl: null,
		//     dob: null,
		//     address: null,
		//     identifierNumber: null,
		//   }),
		// );

		return new UserQuickplayResponseDto({
			qpId: savedUser.username!,
			qpToken: userQuickplayDto.uniqueId,
			createdAt: savedUser.createdAt,
		});
	}

	async loginUserQuickplay(
		userQuickplayDto: UserQuickplayLoginDto,
		ip: string,
	): Promise<UserQuickplayResponseDto> {
		const quickplayUsername = `${SocialCode.get(UserAccountType.QUICKPLAY)}_${userQuickplayDto.username}`;

		const user = await this.findOne({
			username: quickplayUsername,
			accountType: UserAccountType.QUICKPLAY,
		});

		if (!user) {
			throw new NotFoundException('Không tìm thấy người dùng Quickplay');
		}

		// Cập nhật thông tin đăng nhập (không cần await nếu không cần chặn)
		await this.userRepository.update(user.userId, {
			lastLoginAt: new Date(),
			lastLoginAtIp: ip,
		});

		return new UserQuickplayResponseDto({
			qpId: user.username!,
			qpToken: userQuickplayDto.uniqueId,
			createdAt: user.createdAt,
		});
	}

	@Transactional()
	async linkUserQuickplay(
		userQuickplayDto: UserQuickplayLinkDto,
		ip: string,
	): Promise<void> {
		const quickplayUsername = `${SocialCode.get(UserAccountType.QUICKPLAY)}_${userQuickplayDto.qpId}`;

		const user = await this.userRepository.findOneBy({
			username: quickplayUsername,
			accountType: UserAccountType.QUICKPLAY,
		});

		if (!user) {
			throw new NotFoundException('Không tìm thấy người dùng Quickplay');
		}

		// Check nếu đã từng liên kết trước đó
		if (user.accountType !== UserAccountType.QUICKPLAY) {
			throw new BadRequestException('Tài khoản này đã được liên kết trước đó');
		}

		// Kiểm tra username mới có bị trùng không
		const existing = await this.userRepository.findOneBy({
			username: userQuickplayDto.username!,
		});

		if (existing) {
			throw new ConflictException('Username đã tồn tại');
		}

		// Cập nhật thông tin
		user.lastLoginAt = new Date();
		user.lastLoginAtIp = ip;
		user.username = userQuickplayDto.username;
		user.passwordHash = userQuickplayDto.password;
		user.accountType = UserAccountType.LOCAL;

		await this.userRepository.save(user);

		// Tạo user profile nếu chưa có
		if (!user.userProfile) {
			user.userProfile = await this.createUserProfile(
				user.userId,
				plainToClass(MutateUserProfileDto, {
					displayName: null,
					gender: null,
					avatarUrl: null,
					dob: null,
					address: null,
					identifierNumber: null,
				}),
			);
		}
	}

	async getSocialInfo(user: UserAccountEntity): Promise<SocialInfoDto> {
		const isSSOAccount =
			user.accountType === UserAccountType.FACEBOOK ||
			user.accountType === UserAccountType.GOOGLE ||
			user.accountType === UserAccountType.APPLE;

		if (!isSSOAccount) {
			throw new BadRequestException(
				'Tài khoản này không phải là tài khoản đăng nhập mạng xã hội.',
			);
		}

		if (!user.socialUid) {
			throw new NotFoundException(
				'Không tìm thấy UID mạng xã hội cho người dùng này.',
			);
		}

		return new SocialInfoDto({
			socialUid: user.socialUid,
			name: user.userProfile?.displayName || null,
			email: user.email || null,
			avatarUrl: user.userProfile?.avatarUrl || null,
			provider: user.accountType as UserAccountType,
		});
	}

	@Transactional()
	async unlinkSocialAccount(
		user: UserAccountEntity,
		provider: string,
	): Promise<void> {
		const validProviders = [
			UserAccountType.FACEBOOK,
			UserAccountType.GOOGLE,
			UserAccountType.APPLE,
		];

		// Validate: is SSO account type?
		if (!validProviders.includes(user.accountType as UserAccountType)) {
			throw new BadRequestException(
				'Tài khoản này không được liên kết với thông tin đăng nhập mạng xã hội.',
			);
		}

		// Validate: correct provider?
		if (user.accountType !== provider) {
			throw new ConflictException(
				`Tài khoản này không được liên kết với ${provider}.`,
			);
		}

		// Validate: has social UID?
		if (!user.socialUid) {
			throw new NotFoundException(
				'Hiện không có tài khoản xã hội nào được liên kết.',
			);
		}

		// Proceed to unlink
		user.socialUid = null;
		user.socialAccessToken = null;
		user.socialRefreshToken = null;
		user.linkedAt = null;

		// Downgrade account type to LOCAL
		user.accountType = UserAccountType.LOCAL;

		await this.userRepository.save(user);
	}



	createUserProfile(
		userId: number,
		mutateUserProfileDto: MutateUserProfileDto,
	): Promise<UserProfileEntity> {
		return this.commandBus.execute<CreateUserProfileCommand, UserProfileEntity>(
			new CreateUserProfileCommand(userId, mutateUserProfileDto),
		);
	}

	/**
	 * Find user with userProfile relation
	 */
	async findUserWithProfile(userId: number): Promise<UserAccountEntity | null> {
		return this.userRepository.findOne({
			where: { userId },
			relations: ['userProfile'],
		});
	}

	/**
	 * Update verification status for email or phone
	 */
	async updateVerificationStatus(
		emailOrPhone: string,
		verificationType: 'email' | 'phone',
	): Promise<void> {
		// Find user by email or phone
		const whereCondition = verificationType === 'email'
			? { email: emailOrPhone }
			: { phone: emailOrPhone };

		const user = await this.userRepository.findOne({
			where: whereCondition,
		});

		if (!user) {
			throw new UserNotFoundException();
		}

		// Update verification status
		if (verificationType === 'email') {
			user.isEmailVerified = true;
		} else {
			user.isPhoneVerified = true;
		}

		await this.userRepository.save(user);
	}

	updateLastLoginInfo(userId: number, ip: string): Promise<UserAccountEntity> {
		return this.userRepository.save({
			userId,
			lastLoginAt: new Date(),
			lastLoginAtIp: ip,
		});
	}

	updateRefreshToken(
		userId: number,
		refreshToken?: string | null,
	): Promise<UserAccountEntity> {
		const refreshTokenExpiresAt = refreshToken
			? new Date(
				Date.now() +
						this.configService.authConfig.jwtRefreshTokenExpirationTime * 10,
			)
			: new Date(Date.now());

		return this.userRepository.save({
			userId,
			refreshToken,
			refreshTokenExpiresAt,
		});
	}

	async updateUserProfile(
		user: UserAccountEntity,
		mutateUserProfileDto: MutateUserProfileDto,
	): Promise<void> {
		const userProfileEntity = await this.userProfileRepository.findOneBy({
			userId: user.userId,
		});

		if (!userProfileEntity) {
			throw new UserProfileNotFoundException();
		}

		// Update only the fields that are present in the DTO
		Object.assign(userProfileEntity, mutateUserProfileDto);

		await this.userProfileRepository.save(userProfileEntity);

		// Invalidate user cache to reflect the changes
		await this.invalidateUserCache(user.userId);
	}

	async getTransactionHistory(
		user: UserAccountEntity,
		transactionHistoryOptionsDto: TransactionHistoryOptionsDto,
	): Promise<PageDto<TransactionHistoryDto>> {
		// Debug logging to see what values are received
		// console.info('Filter values received:', {
		// 	filterStatus: transactionHistoryOptionsDto.filterStatus,
		// 	dateFrom: transactionHistoryOptionsDto.dateFrom,
		// 	dateTo: transactionHistoryOptionsDto.dateTo,
		// });

		const queryBuilder = this.paymentTransactionRepository
			.createQueryBuilder('payment_transaction')
			.leftJoin('payment_transaction.user', 'user')
			.where('user.userId = :userId', { userId: user.userId })
			.orderBy('payment_transaction.createdAt', 'DESC');

		// Apply status filter if provided
		if (transactionHistoryOptionsDto.filterStatus) {
			// console.info(
			// 	'Applying status filter:',
			// 	transactionHistoryOptionsDto.filterStatus,
			// );
			queryBuilder.andWhere('payment_transaction.status = :status', {
				status: transactionHistoryOptionsDto.filterStatus,
			});
		}

		// Apply date range filter if provided
		if (transactionHistoryOptionsDto.dateFrom) {
			// console.info(
			// 	'Applying dateFrom filter:',
			// 	transactionHistoryOptionsDto.dateFrom,
			// );
			queryBuilder.andWhere('payment_transaction.createdAt >= :dateFrom', {
				dateFrom: transactionHistoryOptionsDto.dateFrom,
			});
		}

		if (transactionHistoryOptionsDto.dateTo) {
			// console.info(
			// 	'Applying dateTo filter:',
			// 	transactionHistoryOptionsDto.dateTo,
			// );
			queryBuilder.andWhere('payment_transaction.createdAt <= :dateTo', {
				dateTo: transactionHistoryOptionsDto.dateTo,
			});
		}

		// Log the final SQL query
		// console.info('Final SQL query:', queryBuilder.getSql());
		// console.info('Query parameters:', queryBuilder.getParameters());

		// Execute query and return paginated results
		const [transactions, total] = await queryBuilder.getManyAndCount();

		const transactionDtos = transactions.map(
			(transaction) =>
				new TransactionHistoryDto({
					txId: transaction.txId,
					gameId: transaction.gameId,
					orderId: transaction.orderId,
					amount: transaction.amount,
					currency: transaction.currency,
					paymentMethod: transaction.paymentMethod as PaymentMethod,
					status: transaction.status as PaymentStatus,
					note: transaction.note,
					createdAt: transaction.createdAt,
				}),
		);

		const page = transactionHistoryOptionsDto.page || 1;
		const take = transactionHistoryOptionsDto.take || 10;
		const pageCount = Math.ceil(total / take);

		return new PageDto(transactionDtos, {
			page,
			take,
			itemCount: total,
			pageCount,
			hasPreviousPage: page > 1,
			hasNextPage: page < pageCount,
		});
	}

	/**
	 * Invalidate user cache when data changes
	 */
	async invalidateUserCache(userId: number): Promise<void> {
		const cacheKey = this.redisKeyManager.user.cache(userId);
		await this.redisService.del(cacheKey);
	}

	/**
	 * Cache user profile data
	 */
	async cacheUserProfile(userId: number, profileData: UserAccountEntity): Promise<void> {
		const key = this.redisKeyManager.user.cache(userId);
		await this.cacheHelper.setWithErrorHandling(key, profileData, 3600, 'user');
	}

	/**
	 * Get cached user profile
	 */
	async getCachedUserProfile(userId: number): Promise<any | null> {
		const key = this.redisKeyManager.user.cache(userId);
		try {
			return await this.redisService.get(key);
		} catch (error) {
			console.warn(`Failed to get cached user profile for ${userId}:`, error);
			// It's better to return null and fetch from the source than to fail
			return null;
		}
	}

	/**
	 * Update user password
	 */
	async updatePassword(userId: number, newPassword: string): Promise<void> {
		// Find the user first
		const user = await this.userRepository.findOne({ where: { userId } });
		if (!user) {
			throw new Error('User not found');
		}

		// Update password (will be hashed by UserSubscriber)
		user.passwordHash = newPassword;
		user.updatedAt = new Date();

		await this.userRepository.save(user);

		// Invalidate user cache
		await this.invalidateUserCache(userId);
	}
}
