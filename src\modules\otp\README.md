# OTP (One-Time Password) Module

## 📋 Overview

The OTP module provides secure one-time password generation, delivery, and verification services for the FS Player Service. It supports both email and Zalo delivery with rate limiting and basic queue processing.

## 🔧 Features

### Core OTP Services
- **Email OTP** - Send OTP via email with customizable templates
- **Zalo OTP** - Send OTP via Zalo messaging platform
- **Auto-Detection** - Automatically detects email vs phone format
- **Rate Limiting** - User and IP-based rate limiting protection
- **Background Processing** - Non-blocking OTP delivery via Redis queue

### Security Features
- **Secure Generation** - Cryptographically secure 6-digit OTP
- **JWT Verification Tokens** - 15-minute verification tokens
- **Rate Limiting** - 3 OTP per user, 10 per IP per 5 minutes
- **Expiration Management** - 5-minute OTP expiration

## 🚀 API Endpoints

### OTP Management
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `POST` | `/otp/send` | Send OTP to email or phone (Admin API) | ✅ Admin |
| `POST` | `/otp/verify` | Verify OTP code | ❌ |

> **Note**: The `/otp/send` HTTP endpoint requires admin authentication, but the `OtpService.sendOtp()` method is used internally by other services (like Auth service for contact verification and password reset) without authorization restrictions.

## 📊 Request/Response Examples

### 📧 Send OTP
**Request:**
```http
POST /otp/send
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "target": "<EMAIL>"
}
```

**Response (201 Created):**
```json
{
  "status": "success",
  "message": "OTP sent successfully",
  "target": "<EMAIL>",
  "contactType": "email",
  "expiresIn": 300,
  "rateLimit": {
    "remaining": 4,
    "resetTime": 3600
  }
}
```

**Request (Phone):**
```http
POST /otp/send
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "target": "84905060708"
}
```

**Response (201 Created):**
```json
{
  "status": "success",
  "message": "OTP sent successfully",
  "target": "84905060708",
  "contactType": "phone",
  "expiresIn": 300,
  "rateLimit": {
    "remaining": 4,
    "resetTime": 3600
  }
}
```

### ✅ Verify OTP
**Request:**
```http
POST /otp/verify
Content-Type: application/json

{
  "target": "<EMAIL>",
  "otp": "123456"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "OTP verified successfully",
  "target": "<EMAIL>",
  "verificationToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "data": {
    "verified": true,
    "verifiedAt": "2025-08-16T10:30:00Z"
  }
}
```

## 🚨 Error Handling

### HTTP Status Codes
| Status Code | Description | Common Scenarios |
|-------------|-------------|------------------|
| `400` | Bad Request | Invalid contact format, expired OTP, rate limit exceeded |
| `401` | Unauthorized | Missing or invalid admin token (for send endpoint) |
| `403` | Forbidden | Non-admin user accessing send endpoint |
| `500` | Internal Server Error | Service unavailable, queue errors |

### Error Response Format
All error responses follow this structure:
```json
{
  "status": "error",
  "message": "Invalid OTP code",
  "errorCode": "INVALID_OTP",
  "details": {
    "attemptsRemaining": 2,
    "cooldownTime": 60
  }
}
```

### Common Error Examples

**Invalid Contact Format (400):**
```json
{
  "status": "error",
  "message": "Must be a valid email address or Vietnamese phone number (84xxxxxxxxx)",
  "errorCode": "INVALID_CONTACT_FORMAT"
}
```

**Rate Limit Exceeded (400):**
```json
{
  "status": "error",
  "message": "Too many OTP requests. Please try again later.",
  "errorCode": "RATE_LIMIT_EXCEEDED",
  "details": {
    "cooldownTime": 300
  }
}
```

**Invalid OTP (400):**
```json
{
  "status": "error",
  "message": "Invalid OTP code",
  "errorCode": "INVALID_OTP"
}
```

**OTP Expired (400):**
```json
{
  "status": "error",
  "message": "OTP has expired. Please request a new one.",
  "errorCode": "OTP_EXPIRED"
}
```

**Service Unavailable (400):**
```json
{
  "status": "error",
  "message": "OTP service is temporarily unavailable",
  "errorCode": "SERVICE_UNAVAILABLE"
}
```

## 🔧 Configuration

### Environment Variables
```bash
# JWT Configuration
JWT_EXPIRATION_TIME=3600
JWT_PRIVATE_KEY=-----BEGIN RSA PRIVATE KEY-----...
JWT_PUBLIC_KEY=-----BEGIN PUBLIC KEY-----...

# Email Configuration
MAILER_API_SECRET=your-mailer-api-secret

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# OTP Settings (via sys-config)
OTP_TTL=300                    # OTP expiration time in seconds
OTP_RATE_LIMIT_USER=5          # Max OTP requests per user per hour
OTP_RATE_LIMIT_IP=20           # Max OTP requests per IP per hour
```

### Dynamic Configuration (via sys-config)
```json
{
  "ACCESS_TOKEN": "zalo-access-token-for-sms",
  "OTP_EMAIL_TEMPLATE": "otp-verification",
  "OTP_ZALO_TEMPLATE": "zalo-otp",
  "OTP_MAX_RETRIES": 3
}
```

## 🏗️ Architecture

### Core Services (Simplified)
- **OtpService** - Main HTTP-based service with rate limiting
- **OtpQueueService** - Simple Redis queue for background delivery
- **OtpWorkerService** - Background worker for processing jobs
- **OtpSecurityService** - Simple OTP generation (no encryption)
- **OtpMetricsService** - Basic daily counters

### Simple Processing Flow
1. **OTP Request** - Admin calls `/otp/send` endpoint
2. **Rate Limiting** - Check user (3/5min) and IP (10/5min) limits
3. **OTP Generation** - Generate secure 6-digit OTP
4. **Storage** - Store OTP in Redis with 5-minute expiration
5. **Queue Job** - Add delivery job to Redis queue (non-blocking)
6. **Background Delivery** - Worker sends via email or Zalo
7. **Verification** - User calls `/otp/verify` with received OTP

## 📝 Implementation Notes

### Access Control
- **HTTP API `/otp/send`**: Requires admin authentication for direct API calls
- **Internal Service Calls**: `OtpService.sendOtp()` used by Auth service without restrictions
- **Public Verification**: Anyone can verify OTP via `/otp/verify`
- **Rate Limiting**: Applied per user and per IP address for all OTP sending
- **Simple Security**: Basic OTP generation and validation

### Delivery Methods
- **Email OTP**: Sent via email service with HTML templates
- **Zalo OTP**: Sent via Zalo messaging using access token
- **Auto-Detection**: Automatically detects email vs phone format
- **Priority Queue**: Email (priority 1), Zalo (priority 2)

### Integration Flow
1. **Admin Request**: Admin calls `/otp/send` with target contact
2. **Validation**: System validates contact format and rate limits
3. **OTP Generation**: Cryptographically secure 6-digit OTP generated
4. **Storage**: OTP stored in Redis with 5-minute expiration
5. **Queue Processing**: Delivery job added to Redis queue
6. **Background Delivery**: Worker processes queue and sends OTP
7. **User Verification**: User calls `/otp/verify` with received OTP
8. **Token Generation**: System returns JWT verification token

### Performance Features
- **Non-blocking Delivery**: OTP sending queued for background processing
- **Redis Pipeline**: Optimized Redis operations for better performance
- **Retry Logic**: Automatic retry with exponential backoff
- **Metrics Tracking**: Real-time success/failure rate monitoring
- **Queue Management**: Priority-based job processing

## 🔗 Related Modules

- [Auth Module](../auth/README.md) - Uses OTP for contact verification
- [Sys-Config Module](../sys-config/README.md) - Dynamic OTP configuration
- [Zalo OAuth Module](../zalo-oauth/README.md) - Provides Zalo access tokens
