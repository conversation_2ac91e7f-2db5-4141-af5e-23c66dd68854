import { HelpersModule } from '@common/helpers/helpers.module';
import { HttpModule } from '@nestjs/axios';
import { forwardRef, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { ApiConfigService } from '@shared/services/api-config.service';
import { SharedModule } from '@shared/shared.module';

import { AuthModule } from '../auth/auth.module';
import { SysConfigModule } from '../sys-config/sys-config.module';
import { UserModule } from '../user/user.module';
import { OtpController } from './otp.controller';
import { OtpService } from './otp.service';
import { OtpMetricsService } from './otp-metrics.service';

@Module({
	imports: [
		HttpModule,
		forwardRef(() => AuthModule), // 👈 xử lý vòng lặp
		forwardRef(() => UserModule), // 👈 xử lý vòng lặp
		SysConfigModule,
		SharedModule,
		HelpersModule,
		JwtModule.registerAsync({
			useFactory: (configService: ApiConfigService) => ({
				privateKey: configService.authConfig.privateKey,
				publicKey: configService.authConfig.publicKey,
				signOptions: {
					algorithm: 'RS256',
					expiresIn: configService.getNumber('JWT_EXPIRATION_TIME'),
				},
				verifyOptions: {
					algorithms: ['RS256'],
				},
			}),
			inject: [ApiConfigService],
		}),
	],
	controllers: [OtpController],
	providers: [
		OtpService, // ✅ Main service
		OtpMetricsService, // ✅ Monitoring
	],
	exports: [OtpService, OtpMetricsService, HttpModule],
})
export class OtpModule {}
