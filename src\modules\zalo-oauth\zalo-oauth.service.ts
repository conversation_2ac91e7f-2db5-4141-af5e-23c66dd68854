// 3. Service để xử lý Zalo OAuth
import { SysConfigService } from '@modules/sys-config/sys-config.service';
import { HttpService } from '@nestjs/axios';
import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import type { AxiosResponse } from 'axios';
import { GeneratorProvider } from 'providers/generator.provider';
import { firstValueFrom } from 'rxjs';

import type { ZaloTokenResponseDto } from './dto/zalo-oauth.dto';

@Injectable()
export class ZaloOAuthService {
	private readonly logger = new Logger(ZaloOAuthService.name);

	// Default Zalo URLs - will be configurable via sys-config
	private readonly DEFAULT_ZALO_OA_URL =
		'https://oauth.zaloapp.com/v4/oa/permission';
	private readonly DEFAULT_ZALO_TOKEN_URL =
		'https://oauth.zaloapp.com/v4/oa/access_token';

	constructor(
		private sysConfigService: SysConfigService,
		private configService: ConfigService,
		private httpService: HttpService,
	) {
		// Default configs are initialized via database migration
	}

	// A) URL callback để đăng ký với Zalo
	getCallbackUrl(): string {
		const baseUrl = this.configService.get<string>('BACKEND_URL');
		return `${baseUrl}/zalo/callback`;
	}

	// URL authorization để redirect người dùng
	async getAuthorizationUrl(): Promise<string> {
		const appId = this.configService.get<string>('ZALO_APP_ID');
		const callbackUrl = this.getCallbackUrl();
		const state = GeneratorProvider.generateRandomString(5);
		const codeVerifier = GeneratorProvider.uuid();
		const codeChallenge = GeneratorProvider.zaloCodeChallenge(codeVerifier);

		// Save temporary OAuth state to sys_config
		await this.setZaloConfig(
			'ZALO_CODE_VERIFIER',
			codeVerifier,
			'Temporary OAuth code verifier',
		);
		await this.setZaloConfig(
			'ZALO_STATE',
			state,
			'Temporary OAuth state parameter',
		);

		const params = new URLSearchParams(<Record<string, string>>{
			app_id: appId,
			redirect_uri: callbackUrl,
			state: state,
			code_challenge: codeChallenge,
		});

		const oauthUrl = await this.getZaloOAuthUrl();
		return `${oauthUrl}?${params.toString()}`;
	}

	async zaloTokenRequest(
		params: Record<string, unknown>,
	): Promise<ZaloTokenResponseDto> {
		try {
			const fullParams = {
				...params,
				app_id: this.configService.get<string>('ZALO_APP_ID'),
				code_verifier: await this.getZaloConfig('ZALO_CODE_VERIFIER'),
			};
			const tokenUrl = await this.getZaloTokenUrl();
			const response: AxiosResponse<ZaloTokenResponseDto> =
				await firstValueFrom(
					this.httpService.post<ZaloTokenResponseDto>(tokenUrl, fullParams, {
						headers: {
							'Content-Type': 'application/x-www-form-urlencoded',
							secret_key: this.configService.get<string>('ZALO_SECRET_KEY'),
						},
					}),
				);

			const tokenData = response.data;
			if (!tokenData) {
				throw new BadRequestException('Zalo OAuth error');
			}

			await this.saveTokensToConfig(tokenData);

			return tokenData;
		} catch (error) {
			if (error instanceof Error) {
				this.logger.error(
					'Error exchanging code for tokens',
					(error as any).response?.data || error.message,
				);
			}
			throw new BadRequestException('Failed to exchange code for tokens');
		}
	}

	// B) Lấy access_token và refresh_token từ authorization code
	async exchangeCodeForTokens(code: string): Promise<ZaloTokenResponseDto> {
		return await this.zaloTokenRequest({
			grant_type: 'authorization_code',
			code,
		});
	}

	// C) Refresh access_token khi hết hạn
	async refreshAccessToken(): Promise<ZaloTokenResponseDto> {
		const refreshToken = await this.getZaloConfig('ZALO_REFRESH_TOKEN');
		if (!refreshToken) {
			throw new BadRequestException('No refresh token available');
		}

		return await this.zaloTokenRequest({
			grant_type: 'refresh_token',
			refresh_token: refreshToken,
		});
	}

	// Lưu tokens vào sys_config table
	private async saveTokensToConfig(
		tokenData: ZaloTokenResponseDto,
	): Promise<void> {
		// Save access token (secret)
		if (tokenData.access_token) {
			await this.setZaloConfig(
				'ZALO_ACCESS_TOKEN',
				tokenData.access_token,
				'Zalo Official Account Access Token',
			);
		}

		// Save refresh token if available (secret)
		if (tokenData.refresh_token) {
			await this.setZaloConfig(
				'ZALO_REFRESH_TOKEN',
				tokenData.refresh_token,
				'Zalo Official Account Refresh Token',
			);
		}

		// Save token expiration time (not secret)
		const expiresAt = new Date(Date.now() + (tokenData.expires_in ?? 0) * 1000);
		await this.setZaloConfig(
			'ZALO_TOKEN_EXPIRES_AT',
			expiresAt.toISOString(),
			'Zalo token expiration timestamp',
		);

		this.logger.log('Zalo tokens saved successfully');
	}

	/**
	 * Helper method to get Zalo config with fallback
	 */
	private async getZaloConfig(
		key: string,
		fallback: string = '',
	): Promise<string> {
		try {
			return await this.sysConfigService.getValue<string>(key);
		} catch (_error) {
			this.logger.warn(
				`Zalo config "${key}" not found, using fallback: ${fallback}`,
			);
			return fallback;
		}
	}

	/**
	 * Get Zalo OAuth authorization URL from config with fallback
	 */
	private async getZaloOAuthUrl(): Promise<string> {
		return await this.getZaloConfig('ZALO_OA_URL', this.DEFAULT_ZALO_OA_URL);
	}

	/**
	 * Get Zalo token URL from config with fallback
	 */
	private async getZaloTokenUrl(): Promise<string> {
		return await this.getZaloConfig(
			'ZALO_TOKEN_URL',
			this.DEFAULT_ZALO_TOKEN_URL,
		);
	}

	/**
	 * Helper method to set Zalo config with proper metadata
	 */
	private async setZaloConfig(
		key: string,
		value: string,
		description: string,
	): Promise<void> {
		await this.sysConfigService.setValue({
			key,
			value,
			description,
			dataType: 'string',
			category: 'zalo',
		});
	}

	/**
	 * Check if Zalo OAuth is properly configured
	 */
	async isConfigured(): Promise<boolean> {
		const appId = this.configService.get<string>('ZALO_APP_ID');
		const appSecret = this.configService.get<string>('ZALO_APP_SECRET');
		const oauthUrl = await this.getZaloConfig('ZALO_OA_URL');
		const tokenUrl = await this.getZaloConfig('ZALO_TOKEN_URL');

		return !!(appId && appSecret && oauthUrl && tokenUrl);
	}

	/**
	 * Get Zalo OAuth status
	 */
	async getOAuthStatus(): Promise<{
		configured: boolean;
		hasAccessToken: boolean;
		tokenExpired: boolean;
		expiresAt?: string;
	}> {
		const configured = await this.isConfigured();
		const accessToken = await this.getZaloConfig('ZALO_ACCESS_TOKEN');
		const expiresAt = await this.getZaloConfig('ZALO_TOKEN_EXPIRES_AT');

		let tokenExpired = true;
		if (expiresAt) {
			tokenExpired = new Date() >= new Date(expiresAt);
		}

		return {
			configured,
			hasAccessToken: !!accessToken,
			tokenExpired,
			expiresAt: expiresAt || undefined,
		};
	}

	/**
	 * Clear all Zalo OAuth configurations
	 */
	async clearOAuthConfig(): Promise<void> {
		const keysToDelete = [
			'ZALO_ACCESS_TOKEN',
			'ZALO_REFRESH_TOKEN',
			'ZALO_TOKEN_EXPIRES_AT',
			'ZALO_CODE_VERIFIER',
			'ZALO_STATE',
		];

		for (const key of keysToDelete) {
			try {
				await this.sysConfigService.deleteConfig(key);
			} catch (error) {
				this.logger.warn(
					`Failed to delete config "${key}": ${error instanceof Error ? error.message : 'Unknown error'}`,
				);
			}
		}

		this.logger.log('Zalo OAuth configuration cleared');
	}

	/**
	 * Update Zalo OAuth URLs (admin only)
	 */
	async updateZaloUrls(oauthUrl?: string, tokenUrl?: string): Promise<void> {
		if (oauthUrl) {
			// Basic URL validation
			if (!oauthUrl.startsWith('https://')) {
				throw new Error('OAuth URL must use HTTPS protocol');
			}

			await this.setZaloConfig(
				'ZALO_OA_URL',
				oauthUrl,
				'Zalo OAuth authorization URL',
			);
			this.logger.log(`Zalo OAuth URL updated to: ${oauthUrl}`);
		}

		if (tokenUrl) {
			// Basic URL validation
			if (!tokenUrl.startsWith('https://')) {
				throw new Error('Token URL must use HTTPS protocol');
			}

			await this.setZaloConfig(
				'ZALO_TOKEN_URL',
				tokenUrl,
				'Zalo OAuth token exchange URL',
			);
			this.logger.log(`Zalo Token URL updated to: ${tokenUrl}`);
		}
	}

	/**
	 * Get current Zalo URLs configuration
	 */
	async getZaloUrls(): Promise<{
		oauthUrl: string;
		tokenUrl: string;
		isDefault: {
			oauthUrl: boolean;
			tokenUrl: boolean;
		};
	}> {
		const oauthUrl = await this.getZaloOAuthUrl();
		const tokenUrl = await this.getZaloTokenUrl();

		return {
			oauthUrl,
			tokenUrl,
			isDefault: {
				oauthUrl: oauthUrl === this.DEFAULT_ZALO_OA_URL,
				tokenUrl: tokenUrl === this.DEFAULT_ZALO_TOKEN_URL,
			},
		};
	}

	// Lấy access token hợp lệ (tự động refresh nếu cần)
	async getValidAccessToken(): Promise<string> {
		const accessToken = await this.getZaloConfig('ZALO_ACCESS_TOKEN');
		const expiresAt = await this.getZaloConfig('ZALO_TOKEN_EXPIRES_AT');

		if (!accessToken) {
			throw new BadRequestException(
				'No access token available. Please authorize first.',
			);
		}

		// Kiểm tra token có hết hạn không (refresh trước 5 phút)
		if (expiresAt) {
			const expireTime = new Date(expiresAt);
			const now = new Date();
			const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000);

			if (expireTime <= fiveMinutesFromNow) {
				this.logger.log('Access token will expire soon, refreshing...');
				const newTokens = await this.refreshAccessToken();
				return newTokens.access_token ?? '';
			}
		}

		return accessToken;
	}

	/**
	 * Reset Zalo URLs to default values (admin only)
	 */
	async resetZaloUrlsToDefault(): Promise<void> {
		await this.setZaloConfig(
			'ZALO_OA_URL',
			this.DEFAULT_ZALO_OA_URL,
			'Zalo OAuth authorization URL',
		);

		await this.setZaloConfig(
			'ZALO_TOKEN_URL',
			this.DEFAULT_ZALO_TOKEN_URL,
			'Zalo OAuth token exchange URL',
		);

		this.logger.log('Zalo URLs reset to default values');
	}
}
