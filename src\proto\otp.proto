syntax = "proto3";

package otp;

// OTP Service Definition
service OtpService {
  // Send email OTP
  rpc SendEmailOtp(SendEmailOtpRequest) returns (SendOtpResponse);
  
  // Send Zalo OTP
  rpc SendZaloOtp(SendZaloOtpRequest) returns (SendOtpResponse);
  
  // Get delivery status
  rpc GetDeliveryStatus(GetDeliveryStatusRequest) returns (GetDeliveryStatusResponse);
  
  // Health check
  rpc HealthCheck(HealthCheckRequest) returns (HealthCheckResponse);
  
  // Streaming delivery updates
  rpc StreamDeliveryUpdates(StreamDeliveryRequest) returns (stream DeliveryUpdate);
}

// Request Messages
message SendEmailOtpRequest {
  string recipient = 1;
  string otp = 2;
  string template = 3;
  map<string, string> context = 4;
  int32 priority = 5;
  string request_id = 6;
  int64 timestamp = 7;
}

message SendZaloOtpRequest {
  string phone = 1;
  string otp = 2;
  string access_token = 3;
  string request_id = 4;
  int64 timestamp = 5;
}

message GetDeliveryStatusRequest {
  string request_id = 1;
}

message HealthCheckRequest {}

message StreamDeliveryRequest {
  repeated string request_ids = 1;
}

// Response Messages
message SendOtpResponse {
  bool success = 1;
  string message = 2;
  string request_id = 3;
  int64 timestamp = 4;
  DeliveryStatus status = 5;
  optional string error_code = 6;
  optional string tracking_id = 7;
}

message GetDeliveryStatusResponse {
  string request_id = 1;
  DeliveryStatus status = 2;
  int64 sent_at = 3;
  optional int64 delivered_at = 4;
  optional string error_message = 5;
  optional string provider_response = 6;
}

message HealthCheckResponse {
  bool healthy = 1;
  string version = 2;
  int64 uptime = 3;
  map<string, string> metrics = 4;
}

message DeliveryUpdate {
  string request_id = 1;
  DeliveryStatus status = 2;
  int64 timestamp = 3;
  optional string message = 4;
}

// Enums
enum DeliveryStatus {
  PENDING = 0;
  QUEUED = 1;
  SENDING = 2;
  SENT = 3;
  DELIVERED = 4;
  FAILED = 5;
  EXPIRED = 6;
}

// Common Types
enum Priority {
  LOW = 0;
  NORMAL = 1;
  HIGH = 2;
  URGENT = 3;
}
