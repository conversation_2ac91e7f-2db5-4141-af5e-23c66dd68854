import { RoleType } from '@constants/role-type';
import { TokenType } from '@constants/token-type';
import { UserService } from '@modules/user/user.service';
import type { UserAccountEntity } from '@modules/user/user-account.entity';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ApiConfigService } from '@shared/services/api-config.service';
import { ExtractJwt, Strategy } from 'passport-jwt';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
	constructor(
		configService: ApiConfigService,
		private userService: UserService,
	) {
		super({
			jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
			secretOrKey: configService.authConfig.publicKey,
		});
	}

	async validate(args: {
		userId: number;
		role: RoleType;
		type: TokenType;
	}): Promise<UserAccountEntity> {
		const isUser =
			args.role === RoleType.USER && args.type === TokenType.ACCESS_TOKEN;
		const isSsoUser =
			args.role === RoleType.SSO_USER && args.type === TokenType.SSO_TOKEN;
		const isAdmin =
			args.role === RoleType.ADMIN && args.type === TokenType.ACCESS_TOKEN;

		if (!(isUser || isSsoUser || isAdmin)) {
			throw new UnauthorizedException();
		}

		const user = await this.userService.findOne({
			userId: args.userId as never,
		});

		if (!user) {
			throw new UnauthorizedException();
		}

		// Attach token role/type onto request.user so downstream guards can authorize by role
		(user as unknown as { role?: RoleType; tokenType?: TokenType }).role =
			args.role;
		(user as unknown as { role?: RoleType; tokenType?: TokenType }).tokenType =
			args.type;

		return user;
	}
}
