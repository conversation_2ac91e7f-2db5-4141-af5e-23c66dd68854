import { UserAccountDto } from '@modules/user/dtos/user-account.dto';
import { ApiProperty } from '@nestjs/swagger';

import { TokenPayloadDto } from './token-payload.dto';

export class ContactVerifyResponseDto {
	@ApiProperty({
		description: 'Operation status',
		enum: ['LOGIN_REQUIRED', 'OTP_SENT'],
		example: 'LOGIN_REQUIRED',
	})
	status!: 'LOGIN_REQUIRED' | 'OTP_SENT';

	@ApiProperty({
		description: 'Detected contact type',
		enum: ['email', 'phone'],
		example: 'email',
	})
	contactType!: 'email' | 'phone';

	@ApiProperty({
		description: 'Human-readable message',
		example: 'User exists. Please login with your password.',
	})
	message?: string;

	@ApiProperty({
		description: 'Additional data for client handling',
		type: 'object',
		properties: {
			userExists: {
				type: 'boolean',
				description: 'Whether user account exists',
				example: true,
			},
			accountStatus: {
				type: 'string',
				description: 'Account status if user exists',
				example: 'ACTIVE',
			},
		},
	})
	data?: {
		userExists: boolean;
		accountStatus?: string;
	};
}

export class RegisterResponseDto {
	@ApiProperty({
		description: 'Operation status',
		example: 'success',
	})
	status!: string;

	@ApiProperty({
		description: 'Success message',
		example: 'Account created successfully',
	})
	message!: string;

	@ApiProperty({
		description: 'User account information',
		type: UserAccountDto,
	})
	user!: UserAccountDto;

	@ApiProperty({
		description: 'Authentication tokens',
		type: TokenPayloadDto,
	})
	tokens!: TokenPayloadDto;

	@ApiProperty({
		description: 'Registration metadata',
		type: 'object',
		properties: {
			registeredAt: {
				type: 'string',
				format: 'date-time',
				description: 'Registration timestamp',
				example: '2025-08-16T10:30:00Z',
			},
			registrationMethod: {
				type: 'string',
				description: 'How user registered',
				example: 'email_otp',
			},
			ipAddress: {
				type: 'string',
				description: 'Registration IP address',
				example: '***********',
			},
		},
	})
	metadata!: {
		registeredAt: string;
		registrationMethod: string;
		ipAddress: string;
	};
}

export class LoginResponseDto {
	@ApiProperty({
		description: 'Operation status',
		example: 'success',
	})
	status!: string;

	@ApiProperty({
		description: 'Success message',
		example: 'Login successful',
	})
	message!: string;

	@ApiProperty({
		description: 'User account information',
		type: UserAccountDto,
	})
	user!: UserAccountDto;

	@ApiProperty({
		description: 'Authentication tokens',
		type: TokenPayloadDto,
	})
	tokens!: TokenPayloadDto;

	@ApiProperty({
		description: 'Login metadata',
		type: 'object',
		properties: {
			loginAt: {
				type: 'string',
				format: 'date-time',
				description: 'Login timestamp',
				example: '2025-08-16T10:30:00Z',
			},
			ipAddress: {
				type: 'string',
				description: 'Login IP address',
				example: '***********',
			},
			userAgent: {
				type: 'string',
				description: 'User agent string',
				example: 'Mozilla/5.0...',
			},
		},
	})
	metadata!: {
		loginAt: string;
		ipAddress: string;
		userAgent?: string;
	};
}

export class ResetPasswordRequestResponseDto {
	@ApiProperty({
		description: 'Operation status',
		example: 'success',
	})
	status!: string;

	@ApiProperty({
		description: 'Success message',
		example: 'Password reset OTP sent successfully',
	})
	message!: string;

	@ApiProperty({
		description: 'Target contact where OTP was sent',
		example: '<EMAIL>',
	})
	target!: string;

	@ApiProperty({
		description: 'OTP expiration time in seconds',
		example: 300,
	})
	expiresIn!: number;
}

export class ResetPasswordResponseDto {
	@ApiProperty({
		description: 'Operation status',
		example: 'success',
	})
	status!: string;

	@ApiProperty({
		description: 'Success message',
		example: 'Password reset successfully',
	})
	message!: string;

	@ApiProperty({
		description: 'Reset metadata',
		type: 'object',
		properties: {
			resetAt: {
				type: 'string',
				format: 'date-time',
				description: 'Password reset timestamp',
				example: '2025-08-16T10:30:00Z',
			},
			method: {
				type: 'string',
				description: 'Reset method used',
				example: 'otp_verification',
			},
		},
	})
	metadata!: {
		resetAt: string;
		method: string;
	};
}

export class RefreshTokenResponseDto {
	@ApiProperty({
		description: 'Operation status',
		example: 'success',
	})
	status!: string;

	@ApiProperty({
		description: 'Success message',
		example: 'Token refreshed successfully',
	})
	message!: string;

	@ApiProperty({
		description: 'New authentication tokens',
		type: TokenPayloadDto,
	})
	tokens!: TokenPayloadDto;

	@ApiProperty({
		description: 'Token metadata',
		type: 'object',
		properties: {
			refreshedAt: {
				type: 'string',
				format: 'date-time',
				description: 'Token refresh timestamp',
				example: '2025-08-16T10:30:00Z',
			},
			expiresAt: {
				type: 'string',
				format: 'date-time',
				description: 'New token expiration',
				example: '2025-08-16T11:30:00Z',
			},
		},
	})
	metadata!: {
		refreshedAt: string;
		expiresAt: string;
	};
}

export class LogoutResponseDto {
	@ApiProperty({
		description: 'Operation status',
		example: 'success',
	})
	status!: string;

	@ApiProperty({
		description: 'Success message',
		example: 'Logged out successfully',
	})
	message!: string;

	@ApiProperty({
		description: 'Logout metadata',
		type: 'object',
		properties: {
			logoutAt: {
				type: 'string',
				format: 'date-time',
				description: 'Logout timestamp',
				example: '2025-08-16T10:30:00Z',
			},
		},
	})
	metadata!: {
		logoutAt: string;
	};
}

export class AuthErrorResponseDto {
	@ApiProperty({
		description: 'Error status',
		example: 'error',
	})
	status!: string;

	@ApiProperty({
		description: 'Error message',
		example: 'Invalid credentials',
	})
	message!: string;

	@ApiProperty({
		description: 'Error code for client handling',
		example: 'INVALID_CREDENTIALS',
	})
	errorCode!: string;

	@ApiProperty({
		description: 'Additional error details',
		type: 'object',
		properties: {
			field: {
				type: 'string',
				description: 'Field that caused the error',
				example: 'password',
			},
			attemptsRemaining: {
				type: 'number',
				description: 'Remaining login attempts',
				example: 2,
			},
			lockoutTime: {
				type: 'number',
				description: 'Account lockout time in seconds',
				example: 300,
			},
		},
	})
	details?: {
		field?: string;
		attemptsRemaining?: number;
		lockoutTime?: number;
	};
}
