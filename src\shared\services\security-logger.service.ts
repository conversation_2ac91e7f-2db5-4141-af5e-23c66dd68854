import { Injectable } from '@nestjs/common';

import { RedisService } from './redis.service';

export interface SecurityEvent {
	type:
		| 'LOGIN_SUCCESS'
		| 'LOGIN_FAILED'
		| 'PASSWORD_RESET'
		| 'ACCOUNT_LOCKED'
		| 'SUSPICIOUS_ACTIVITY';
	userId?: number;
	username?: string;
	ip: string;
	userAgent?: string;
	timestamp: Date;
	details?: any;
}

@Injectable()
export class SecurityLoggerService {
	constructor(private readonly redisService: RedisService) {}

	/**
	 * Log security events
	 */
	async logSecurityEvent(event: SecurityEvent): Promise<void> {
		try {
			const logEntry = {
				...event,
				timestamp: new Date().toISOString(),
			};

			// Store in Redis with TTL (30 days)
			const key = `security_log:${Date.now()}:${Math.random().toString(36).substr(2, 9)}`;
			await this.redisService.set(key, logEntry, 2592000); // 30 days

			// Also log to console for immediate visibility
			console.log(`[SECURITY] ${event.type}:`, {
				userId: event.userId,
				username: event.username,
				ip: event.ip,
				timestamp: event.timestamp,
			});

			// Store aggregated stats
			await this.updateSecurityStats(event);
		} catch (error) {
			console.error('Failed to log security event:', error);
		}
	}

	/**
	 * Update security statistics
	 */
	private async updateSecurityStats(event: SecurityEvent): Promise<void> {
		const today = new Date().toISOString().split('T')[0];
		const statsKey = `security_stats:${today}`;

		try {
			const stats = (await this.redisService.get<any>(statsKey)) || {};

			if (!stats[event.type]) {
				stats[event.type] = 0;
			}

			stats[event.type]++;

			await this.redisService.set(statsKey, stats, 86400); // 24 hours
		} catch (error) {
			console.error('Failed to update security stats:', error);
		}
	}

	/**
	 * Get security stats for a date
	 */
	async getSecurityStats(date: string): Promise<any> {
		const statsKey = `security_stats:${date}`;
		return (await this.redisService.get(statsKey)) || {};
	}

	/**
	 * Check for suspicious activity patterns
	 */
	async checkSuspiciousActivity(ip: string, userId?: number): Promise<boolean> {
		try {
			// Check failed login attempts from IP
			const ipFailures =
				(await this.redisService.get<number>(`login_attempts_ip:${ip}`)) || 0;

			if (ipFailures >= 10) {
				await this.logSecurityEvent({
					type: 'SUSPICIOUS_ACTIVITY',
					userId,
					ip,
					timestamp: new Date(),
					details: {
						reason: 'High failed login attempts from IP',
						count: ipFailures,
					},
				});
				return true;
			}

			return false;
		} catch (error) {
			console.error('Failed to check suspicious activity:', error);
			return false;
		}
	}
}
