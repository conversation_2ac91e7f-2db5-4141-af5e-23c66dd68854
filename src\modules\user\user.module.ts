import { HelpersModule } from '@common/helpers/helpers.module';
import { PaymentTransactionEntity } from '@modules/payment/payment-transaction.entity';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharedModule } from '@shared/shared.module';

import { CreateUserProfileHandler } from './commands/create-user-profile.command';
import { UserController } from './user.controller';
import { UserService } from './user.service';
import { UserAccountEntity } from './user-account.entity';
import { UserProfileEntity } from './user-profile.entity';

const handlers = [CreateUserProfileHandler];

@Module({
	imports: [
		TypeOrmModule.forFeature([
			UserAccountEntity,
			UserProfileEntity,
			PaymentTransactionEntity,
		]),
		SharedModule,
		HelpersModule,
	],
	controllers: [UserController],
	exports: [UserService],
	providers: [UserService, ...handlers],
})
export class UserModule {}
