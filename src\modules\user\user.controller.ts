import { PageDto } from '@common/dto/page.dto';
import { ResponseDto } from '@common/dto/response.dto.ts';
import { getIp } from '@common/utils.ts';
import { RoleType } from '@constants/role-type';
import { ApiPageResponse } from '@decorators/api-page-response.decorator';
import { AuthUser } from '@decorators/auth-user.decorator';
import { Auth } from '@decorators/http.decorators';
import { SocialInfoDto } from '@modules/auth/dto/social-info.dto.ts';
import { TransactionHistoryDto } from '@modules/payment/dtos/transaction-history.dto';
import { TransactionHistoryOptionsDto } from '@modules/payment/dtos/transaction-history-options.dto';
import {
	Body,
	Controller,
	Delete,
	Get,
	HttpCode,
	HttpStatus,
	Param,
	Post,
	Put,
	Query,
	Req,
	ValidationPipe,
	Version,
} from '@nestjs/common';
import {
	ApiAcceptedResponse,
	ApiBody,
	ApiOkResponse,
	ApiTags,
} from '@nestjs/swagger';
import type { Request } from 'express';

import { MutateUserProfileDto } from './dtos/mutate-user-profile.dto.ts';
import { UserProfileResponseDto } from './dtos/user-profile-response.dto.ts';
import type { UserQuickplayLinkDto } from './dtos/user-quickplay-link.dto.ts';
import type { UserQuickplayLoginDto } from './dtos/user-quickplay-login.dto.ts';
import { UserQuickplayResponseDto } from './dtos/user-quickplay-response.dto.ts';
import { UserService } from './user.service.ts';
import type { UserAccountEntity } from './user-account.entity.ts';

@Controller('user')
@ApiTags('User')
export class UserController {
	constructor(
		private userService: UserService,
	) {}

	@Version('1')
	@Get('profile')
	@HttpCode(HttpStatus.OK)
	@Auth([RoleType.USER])
	@ApiOkResponse({ type: UserProfileResponseDto, description: 'current user profile with profile data' })
	async getUserProfile(@AuthUser() user: UserAccountEntity): Promise<UserProfileResponseDto> {
		// Load user with userProfile relation to get complete profile data
		const userWithProfile = await this.userService.findUserWithProfile(user.userId);

		if (!userWithProfile) {
			// This should not happen since user is authenticated, but handle gracefully
			return new UserProfileResponseDto(user);
		}

		return new UserProfileResponseDto(userWithProfile);
	}

	@Version('1')
	@Put('profile')
	@HttpCode(HttpStatus.ACCEPTED)
	@ApiAcceptedResponse()
	@Auth([RoleType.USER])
	@ApiBody({ type: MutateUserProfileDto })
	@ApiOkResponse({ type: ResponseDto, description: 'update user profile' })
	async updateUserProfile(
		@AuthUser() user: UserAccountEntity,
		@Body() mutateUserProfileDto: MutateUserProfileDto,
	): Promise<ResponseDto<null>> {
		return this.userService.updateUserProfile(user, mutateUserProfileDto);
	}

	@Version('1')
	@Post('quickplay')
	@HttpCode(HttpStatus.OK)
	@Auth([RoleType.USER])
	@ApiOkResponse({
		type: UserQuickplayResponseDto,
		description: 'create quickplay user',
	})
	async createQuickplayUser(
		@Body() userQuickplayDto: UserQuickplayLoginDto,
		@Req() request: Request,
	): Promise<UserQuickplayResponseDto> {
		if (userQuickplayDto.username) {
			const res = this.userService.loginUserQuickplay(
				userQuickplayDto,
				getIp(request),
			);

			if (res instanceof Promise) {
				return res;
			}
		}

		return this.userService.createUserQuickplay(
			userQuickplayDto,
			getIp(request),
		);
	}

	@Version('1')
	@Post('quickplay/link')
	@HttpCode(HttpStatus.OK)
	@Auth([RoleType.USER])
	@ApiOkResponse({
		type: UserQuickplayResponseDto,
		description: 'link quickplay user',
	})
	async linkUserQuickplay(
		@Body() userQuickplayDto: UserQuickplayLinkDto,
		@Req() request: Request,
	): Promise<ResponseDto<null>> {
		return this.userService.linkUserQuickplay(userQuickplayDto, getIp(request));
	}

	@Version('1')
	@Get('social/account')
	@HttpCode(HttpStatus.OK)
	@Auth([RoleType.USER])
	@ApiOkResponse({
		type: SocialInfoDto,
		description: 'Get social account info',
	})
	async getSocialInfo(
		@AuthUser() user: UserAccountEntity,
	): Promise<SocialInfoDto> {
		return this.userService.getSocialInfo(user);
	}

	@Version('1')
	@Delete('social/account/:provider')
	@HttpCode(HttpStatus.OK)
	@Auth([RoleType.USER])
	@ApiOkResponse({
		type: ResponseDto,
		description: 'Unlink social account',
	})
	async unlinkSocialAccount(
		@AuthUser() user: UserAccountEntity,
		@Param('provider') provider: string,
	): Promise<ResponseDto<null>> {
		return this.userService.unlinkSocialAccount(user, provider);
	}

	@Version('1')
	@Get('transactions/history')
	@HttpCode(HttpStatus.OK)
	@Auth([RoleType.USER])
	@ApiPageResponse({
		type: PageDto<TransactionHistoryDto>,
		description: 'Get transaction history',
	})
	async getTransactionHistory(
		@AuthUser() user: UserAccountEntity,
		@Query(new ValidationPipe({ transform: true }))
		transactionHistoryOptionsDto: TransactionHistoryOptionsDto,
	): Promise<PageDto<TransactionHistoryDto>> {
		return this.userService.getTransactionHistory(
			user,
			transactionHistoryOptionsDto,
		);
	}


}
