import { RoleType } from '@constants/role-type';
import { Auth } from '@decorators/http.decorators';
import { Body, Controller, Ip, Post } from '@nestjs/common';
import {
	ApiBadRequestResponse,
	ApiBody,
	ApiCreatedResponse,
	ApiOkResponse,
	ApiOperation,
	ApiTags,
} from '@nestjs/swagger';

import { SendOtpDto, VerifyOtpDto } from './dto/otp.dto';
import {
	OtpErrorResponseDto,
	SendOtpResponseDto,
	VerifyOtpResponseDto,
} from './dto/otp-response.dto';
import { OtpService } from './otp.service';

@ApiTags('OTP')
@Controller('otp')
export class OtpController {
	constructor(private readonly otpService: OtpService) {}

	@Post('send')
	@ApiOperation({
		summary: 'Send OTP to email or phone number (Admin Only)',
		description:
			'Sends a one-time password to the specified email address or Vietnamese phone number. Auto-detects contact type and applies rate limiting.',
	})
	@Auth([RoleType.ADMIN])
	@ApiBody({ type: SendOtpDto })
	@ApiCreatedResponse({
		description: 'OTP sent successfully',
		type: SendOtpResponseDto,
		schema: {
			example: {
				status: 'success',
				message: 'OTP sent successfully',
				target: '<EMAIL>',
				contactType: 'email',
				expiresIn: 300,
				rateLimit: {
					remaining: 4,
					resetTime: 3600,
				},
			},
		},
	})
	@ApiBadRequestResponse({
		description:
			'Bad request - invalid email/phone, rate limit exceeded, or service unavailable',
		type: OtpErrorResponseDto,
		schema: {
			examples: {
				invalidContact: {
					summary: 'Invalid contact format',
					value: {
						status: 'error',
						message:
							'Must be a valid email address or Vietnamese phone number (84xxxxxxxxx)',
						errorCode: 'INVALID_CONTACT_FORMAT',
					},
				},
				rateLimitExceeded: {
					summary: 'Rate limit exceeded',
					value: {
						status: 'error',
						message: 'Too many OTP requests. Please try again later.',
						errorCode: 'RATE_LIMIT_EXCEEDED',
						details: {
							cooldownTime: 300,
						},
					},
				},
				serviceUnavailable: {
					summary: 'Service unavailable',
					value: {
						status: 'error',
						message: 'OTP service is temporarily unavailable',
						errorCode: 'SERVICE_UNAVAILABLE',
					},
				},
			},
		},
	})
	sendOtp(@Body() dto: SendOtpDto, @Ip() ip: string) {
		return this.otpService.sendOtp(dto.target, ip);
	}

	@Post('verify')
	@ApiOperation({
		summary: 'Verify OTP code',
		description:
			'Verifies the OTP code sent to the specified email or phone number. Returns verification status and optional token for next steps.',
	})
	@ApiBody({ type: VerifyOtpDto })
	@ApiOkResponse({
		description: 'OTP verified successfully',
		type: VerifyOtpResponseDto,
		schema: {
			example: {
				status: 'success',
				message: 'OTP verified successfully',
				target: '<EMAIL>',
				verificationToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
				data: {
					verified: true,
					verifiedAt: '2025-08-16T10:30:00Z',
				},
			},
		},
	})
	@ApiBadRequestResponse({
		description: 'Bad request - invalid, expired, or already used OTP',
		type: OtpErrorResponseDto,
		schema: {
			examples: {
				invalidOtp: {
					summary: 'Invalid OTP code',
					value: {
						status: 'error',
						message: 'Invalid OTP code',
						errorCode: 'INVALID_OTP',
						details: {
							attemptsRemaining: 2,
						},
					},
				},
				expiredOtp: {
					summary: 'Expired OTP',
					value: {
						status: 'error',
						message: 'OTP has expired. Please request a new one.',
						errorCode: 'OTP_EXPIRED',
					},
				},
				tooManyAttempts: {
					summary: 'Too many failed attempts',
					value: {
						status: 'error',
						message:
							'Too many failed verification attempts. Please try again later.',
						errorCode: 'TOO_MANY_ATTEMPTS',
						details: {
							cooldownTime: 300,
						},
					},
				},
				alreadyUsed: {
					summary: 'OTP already used',
					value: {
						status: 'error',
						message: 'OTP has already been used',
						errorCode: 'OTP_ALREADY_USED',
					},
				},
			},
		},
	})
	verifyOtp(@Body() dto: VerifyOtpDto) {
		return this.otpService.verifyOtp(dto.target, dto.otp);
	}
}
