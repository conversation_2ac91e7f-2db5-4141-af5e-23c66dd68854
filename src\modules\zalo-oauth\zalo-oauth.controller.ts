import { RoleType } from '@constants/role-type';
import { ApiCommonResponse } from '@decorators/api-common-response.decorator';
import { Auth } from '@decorators/http.decorators';
import { RawResponse } from '@decorators/raw-response.decorator';
import {
	BadRequestException,
	Controller,
	Get,
	Logger,
	Post,
	Query,
	Res,
} from '@nestjs/common';
import {
	ApiBadRequestResponse,
	ApiOperation,
	ApiResponse,
	ApiTags,
} from '@nestjs/swagger';
import { type Response } from 'express';

import {
	ConnectionStatusDto,
	ZaloCallbackDto,
	ZaloTokenResponseDto,
	ZaloUrlsDto,
} from './dto/zalo-oauth.dto';
import { ZaloOAuthService } from './zalo-oauth.service';

@ApiTags('Zalo OAuth')
@Controller('zalo')
export class ZaloOAuthController {
	private readonly logger = new Logger(ZaloOAuthController.name);

	constructor(private readonly zaloOAuthService: ZaloOAuthService) {}

	@Get('authorize')
	@RawResponse()
	@ApiOperation({ summary: 'Start Zalo OAuth authorization flow (Admin only)' })
	@ApiResponse({
		status: 302,
		description: 'Redirects to Zalo authorization URL',
	})
	async authorize(@Res() res: Response) {
		const authUrl = await this.zaloOAuthService.getAuthorizationUrl();
		// console.info('authUrl: ', authUrl);
		return res.redirect(authUrl);
	}

	@Get('callback')
	@RawResponse()
	@ApiOperation({ summary: 'Handle Zalo OAuth callback' })
	@ApiResponse({
		status: 302,
		description: 'Redirects to success page or returns error',
	})
	@ApiResponse({ status: 400, description: 'OAuth error or invalid callback' })
	async handleCallback(@Query() query: ZaloCallbackDto, @Res() res: Response) {
		try {
			if (!query.code) {
				throw new BadRequestException('Authorization code not provided');
			}

			await this.zaloOAuthService.exchangeCodeForTokens(query.code);
			this.logger.log('Successfully obtained tokens from Zalo');
			return res.status(200).json({
				message: 'OK',
			});
		} catch (error) {
			this.logger.error('Callback handling error', (error as Error).message);
			return res.status(400).json({
				error: 'callback_error',
				message: (error as Error).message,
			});
		}
	}

	@Get('status')
	@Auth([RoleType.ADMIN])
	@ApiOperation({ summary: 'Check Zalo OAuth connection status (Admin only)' })
	@ApiCommonResponse({
		type: ConnectionStatusDto,
		description: 'Connection status information',
	})
	async getConnectionStatus(): Promise<ConnectionStatusDto> {
		try {
			const status = await this.zaloOAuthService.getOAuthStatus();
			const accessToken =
        status.hasToken && !status.tokenExpired
        	? await this.zaloOAuthService.getValidAccessToken()
        	: null;

			return {
				connected:
          status.configured && status.hasToken && !status.tokenExpired,
				hasToken: status.hasToken,
				tokenPrefix: accessToken ? accessToken.substring(0, 20) + '...' : null,
				configured: status.configured,
				tokenExpired: status.tokenExpired,
				expiresAt: status.expiresAt,
			};
		} catch (error) {
			return {
				connected: false,
				hasToken: false,
				configured: false,
				tokenExpired: true,
				error: (error as Error).message,
			};
		}
	}

	@Get('refresh')
	@Auth([RoleType.ADMIN])
	@ApiOperation({ summary: 'Force refresh Zalo access token (Admin only)' })
	@ApiCommonResponse({
		type: ZaloTokenResponseDto,
		description: 'Token refreshed successfully',
	})
	@ApiBadRequestResponse({ description: 'Failed to refresh token' })
	async refreshToken(): Promise<ZaloTokenResponseDto> {
		return this.zaloOAuthService.refreshAccessToken();
	}

	@Get('urls')
	@Auth([RoleType.ADMIN])
	@ApiOperation({
		summary: 'Get Zalo OAuth URLs configuration (Admin only)',
		description: 'Get current OAuth and token URLs with default status',
	})
	@ApiCommonResponse({
		type: ZaloUrlsDto,
		description: 'Zalo URLs configuration',
	})
	async getZaloUrls(): Promise<ZaloUrlsDto> {
		return this.zaloOAuthService.getZaloUrls();
	}

	@Post('urls/reset')
	@Auth([RoleType.ADMIN])
	@ApiOperation({
		summary: 'Reset Zalo URLs to default values (Admin only)',
		description: 'Reset OAuth and token URLs back to default Zalo endpoints',
	})
	@ApiCommonResponse({ description: 'URLs reset successfully' })
	async resetZaloUrls(): Promise<void> {
		await this.zaloOAuthService.resetZaloUrlsToDefault();
	}
}